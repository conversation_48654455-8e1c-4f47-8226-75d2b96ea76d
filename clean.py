import json
import re

def is_valid_output(output: str) -> bool:
    """
    Returns True if the output ends with '.', '?', or '!', ignoring whitespace.
    """
    output = output.rstrip()
    return bool(output) and output[-1] in {'.', '?', '!'}


def clean_json_rows(input_file: str, output_file: str):
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    if not isinstance(data, list):
        raise ValueError("Input JSON must be a list of objects.")
    cleaned = [row for row in data if is_valid_output(row.get('output', ''))]
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(cleaned, f, ensure_ascii=False, indent=2)
    print(f"Removed {len(data) - len(cleaned)} corrupted rows. Cleaned data saved to {output_file}.")

if __name__ == "__main__":
    clean_json_rows('3.json', '3_clean.json')
