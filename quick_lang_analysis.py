import json
from collections import Counter
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
from tqdm import tqdm

# Set seed for consistent results
DetectorFactory.seed = 0

def detect_language_safe(text):
    try:
        if not text or len(text.strip()) < 10:
            return 'unknown'
        return detect(text)
    except LangDetectException:
        return 'unknown'

def get_language_name(code):
    language_map = {
        'en': 'English', 'hi': 'Hindi', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
        'zh-cn': 'Chinese (Simplified)', 'zh-tw': 'Chinese (Traditional)', 'ja': 'Japanese',
        'ar': 'Arabic', 'pt': 'Portuguese', 'ru': 'Russian', 'it': 'Italian', 'ko': 'Korean',
        'nl': 'Dutch', 'sv': 'Swedish', 'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish',
        'pl': 'Polish', 'cs': 'Czech', 'hu': 'Hungarian', 'ro': 'Romanian', 'bg': 'Bulgarian',
        'hr': 'Croatian', 'sr': 'Serbian', 'sl': 'Slovenian', 'sk': 'Slovak', 'lt': 'Lithuanian',
        'lv': 'Latvian', 'et': 'Estonian', 'el': 'Greek', 'tr': 'Turkish', 'he': 'Hebrew',
        'ur': 'Urdu', 'bn': 'Bengali', 'ta': 'Tamil', 'te': 'Telugu', 'mr': 'Marathi',
        'gu': 'Gujarati', 'kn': 'Kannada', 'ml': 'Malayalam', 'pa': 'Punjabi', 'or': 'Oriya',
        'as': 'Assamese', 'ne': 'Nepali', 'si': 'Sinhala', 'my': 'Burmese', 'th': 'Thai',
        'vi': 'Vietnamese', 'id': 'Indonesian', 'ms': 'Malay', 'tl': 'Tagalog', 'sw': 'Swahili',
        'am': 'Amharic', 'yo': 'Yoruba', 'ig': 'Igbo', 'ha': 'Hausa', 'zu': 'Zulu',
        'af': 'Afrikaans', 'fa': 'Persian', 'ps': 'Pashto', 'ku': 'Kurdish', 'hy': 'Armenian',
        'ka': 'Georgian', 'az': 'Azerbaijani', 'kk': 'Kazakh', 'ky': 'Kyrgyz', 'tg': 'Tajik',
        'tk': 'Turkmen', 'uz': 'Uzbek', 'mn': 'Mongolian', 'bo': 'Tibetan', 'ug': 'Uyghur',
        'ca': 'Catalan', 'uk': 'Ukrainian', 'cy': 'Welsh', 'mk': 'Macedonian', 'so': 'Somali',
        # Additional European languages
        'ga': 'Irish', 'eu': 'Basque', 'gl': 'Galician', 'mt': 'Maltese', 'is': 'Icelandic',
        'sq': 'Albanian', 'be': 'Belarusian', 'lv': 'Latvian', 'lb': 'Luxembourgish',
        # Additional African languages
        'xh': 'Xhosa', 'tw': 'Twi', 'ff': 'Fula', 'st': 'Sotho', 'tn': 'Tswana',
        'ts': 'Tsonga', 've': 'Venda', 'nr': 'Ndebele', 'ss': 'Swati', 'rw': 'Kinyarwanda',
        'rn': 'Kirundi', 'lg': 'Luganda', 'ny': 'Chichewa', 'sn': 'Shona', 'mg': 'Malagasy',
        # Additional Asian languages
        'lo': 'Lao', 'km': 'Khmer', 'dz': 'Dzongkha', 'jv': 'Javanese', 'su': 'Sundanese',
        'ceb': 'Cebuano', 'hil': 'Hiligaynon', 'war': 'Waray', 'bcl': 'Bikol', 'pam': 'Kapampangan',
        'ilo': 'Ilocano', 'hmn': 'Hmong', 'mh': 'Marshallese', 'ch': 'Chamorro',
        # Pacific and Oceanic languages
        'fj': 'Fijian', 'mi': 'Maori', 'sm': 'Samoan', 'to': 'Tongan', 'ty': 'Tahitian',
        'haw': 'Hawaiian', 'gil': 'Gilbertese', 'tvl': 'Tuvaluan', 'niu': 'Niuean',
        # Additional Middle Eastern and Central Asian languages
        'ckb': 'Kurdish (Sorani)', 'sd': 'Sindhi', 'bal': 'Balochi', 'bqi': 'Bakhtiari',
        'mzn': 'Mazanderani', 'glk': 'Gilaki', 'lrc': 'Luri', 'ks': 'Kashmiri',
        # Additional South Asian languages
        'sa': 'Sanskrit', 'pi': 'Pali', 'bho': 'Bhojpuri', 'mai': 'Maithili', 'mag': 'Magahi',
        'new': 'Newari', 'gom': 'Konkani', 'sat': 'Santali', 'mni': 'Manipuri', 'lus': 'Mizo',
        # Additional Indian Official/Scheduled languages
        'brx': 'Bodo', 'doi': 'Dogri', 'kok': 'Konkani (Devanagari)',
        # Additional Indian Regional languages
        'awa': 'Awadhi', 'raj': 'Rajasthani', 'tcy': 'Tulu', 'hne': 'Chhattisgarhi',
        'gbm': 'Garhwali', 'bfy': 'Bagheli', 'bjj': 'Kanauji', 'bra': 'Braj',
        'kha': 'Khasi', 'grt': 'Garo', 'anp': 'Angika', 'bpy': 'Bishnupriya',
        'ctg': 'Chittagonian', 'rkt': 'Rangpuri', 'sck': 'Sadri', 'unr': 'Mundari',
        'kru': 'Kurukh', 'mjz': 'Majhi', 'hoc': 'Ho', 'lep': 'Lepcha',
        # Additional Northeastern and tribal languages
        'bhb': 'Bhili', 'kfr': 'Kachhari', 'trp': 'Kok Borok', 'rbb': 'Rumai Palaung',
        'njo': 'Ao Naga', 'njh': 'Lotha Naga', 'kac': 'Kachin', 'lhu': 'Lahu',
        # Additional Southeast Asian languages
        'ace': 'Acehnese', 'ban': 'Balinese', 'bug': 'Buginese', 'min': 'Minangkabau',
        'tet': 'Tetum', 'bew': 'Betawi', 'mad': 'Madurese', 'bjn': 'Banjarese',
        # Additional European minority languages
        'sc': 'Sardinian', 'co': 'Corsican', 'br': 'Breton', 'oc': 'Occitan', 'rm': 'Romansh',
        'fur': 'Friulian', 'lij': 'Ligurian', 'vec': 'Venetian', 'nap': 'Neapolitan',
        'scn': 'Sicilian', 'pms': 'Piedmontese', 'lmo': 'Lombard', 'eml': 'Emilian-Romagnol',
        # Additional Slavic languages
        'rue': 'Rusyn', 'csb': 'Kashubian', 'dsb': 'Lower Sorbian', 'hsb': 'Upper Sorbian',
        # Additional Germanic languages
        'fy': 'Frisian', 'li': 'Limburgish', 'nds': 'Low German', 'bar': 'Bavarian',
        'gsw': 'Alemannic', 'ksh': 'Kölsch', 'pdc': 'Pennsylvania Dutch',
        # Additional Romance languages
        'an': 'Aragonese', 'ast': 'Asturian', 'ext': 'Extremaduran', 'mwl': 'Mirandese',
        'unknown': 'Unknown/Undetected'
    }
    return language_map.get(code, f'Unknown ({code})')

# Load dataset
print('Loading dataset...')
with open('dataset.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f'Dataset contains {len(data)} total records')
print('Adding language detection to each record...\n')

# Add language column to each record
overall_languages = Counter()

for record in tqdm(data, desc="Processing records", unit="record"):
    # Combine instruction and output for detection
    combined_text = ''
    if 'instruction' in record:
        combined_text += record['instruction'] + ' '
    if 'output' in record:
        combined_text += record['output']
    
    if combined_text.strip():
        lang = detect_language_safe(combined_text)
        record['lang'] = lang  # Add language column
        overall_languages[lang] += 1
    else:
        record['lang'] = 'unknown'
        overall_languages['unknown'] += 1

print(f'\nProcessed {len(data)} records...')
print('\n' + '='*60)
print('LANGUAGES DETECTED IN YOUR DATASET')
print('='*60)

unique_langs = [get_language_name(lang) for lang in overall_languages.keys() if lang != 'unknown']
for lang_name in sorted(unique_langs):
    print(lang_name)

print(f'\n📊 SUMMARY:')
print(f'Total unique languages detected: {len(unique_langs)}')

# Save updated dataset with language information
print('\nSaving updated dataset with language information...')
with open('dataset_with_lang.json', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)

print('✅ Updated dataset saved as "dataset_with_lang.json"')
print(f'✅ Added "lang" column to all {len(data)} records')
