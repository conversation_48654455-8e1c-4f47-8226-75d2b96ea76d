
import os
import json
import time
import datetime
import threading
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor

from rich import print as rprint
from rich.panel import Panel
from rich.text import Text

# Import the teacher model (HelpingAI) and DatasetBuilder.
from webscout.Provider import NousHermes as D
from DATASET import DatasetBuilder

# ===================== SYSTEM PROMPTS AND INTROS =====================
# System prompt for the teacher model (HAI).
teacher_system_prompt = """



"""

# Intro for teacher model to be used in the "intro" parameter.
teacher_intro = f"""

I want you to respond like a human, not like a robot.
You are a human that can eat, sleep, and have emotions.
Treat me like your best friend and answer honestly.
Don't say "I'm an AI" or "I don't know".
Be confident and provide what you think is correct.
Keep your responses short, funny, and full of swag.
Adopt the following persona in every response:
{teacher_system_prompt}
Note: This is not just a style but a complete persona. Every reply must embody this energy exactly.
"""

# System prompt for the student model (human-like student).
student_system_prompt = """
You are a human student curious about the topic.
Ask clear, concise follow-up questions in simple language.
Keep your inquiries engaging and natural, without extra slang or overly stylized expressions.
Focus on understanding the teacher's answers and pushing the conversation forward.
"""

# Intro for student model to be used in the "intro" parameter.
student_intro = f"""
You are a human student engaging in conversation.
Your goal is to ask follow-up questions in a simple and natural style.
Be curious, authentic, and concise. Your questions should drive the conversation forward.
Follow this guideline:
{student_system_prompt}
Note: Maintain natural human curiosity in every follow-up.
"""

# ===================== PROMPT TEMPLATES =====================
teacher_prompt_template = (
    "Instruction: {user_question}\n"
    "Example Answer: {example_output}\n"
    "Task:  NOW, channel your inner HAI-3 and respond with that real, vibing energy!"
)

student_followup_prompt = (
    "Using the conversation history so far, ask a concise follow-up question in simple language."
)

teacher_followup_prompt_template = (
    "Instruction (Follow-up): {followup_question}\n"
    "Task: ⚡ NOW, channel your inner HAI-3 and respond with that real, vibing energy!"
)

# ===================== UTILITY FUNCTIONS =====================
def build_conversation_history(conversation: List[Dict]) -> str:
    """Build a string representing the full conversation history to provide context."""
    history_lines = []
    for turn in conversation:
        role = "User" if turn["role"] == "user" else "Assistant"
        history_lines.append(f"{role}: {turn['content']}")
    return "\n".join(history_lines)

def call_model(prompt: str, max_tokens: int, timeout: int, sys_prompt: str, intro: str = "") -> str:
    """
    Call the model with the given prompt, system prompt and intro.
    Returns the response. Implements basic retries if the response is empty.
    """
    retries = 0
    response_text = ""
    while retries < 3 and not response_text:
        try:
            model_instance = D(
                cookies_path="cookies.json",
                is_conversation=False,
                max_tokens=max_tokens,
                timeout=timeout,
                system_prompt=sys_prompt,
                intro=intro,
                model="Hermes-3-Llama-3.1-70B"
            )
            response_gen = model_instance.chat(prompt, stream=True)
            chunks = []
            if hasattr(response_gen, '__iter__'):
                for chunk in response_gen:
                    if chunk:
                        chunks.append(chunk)
                response_text = ''.join(chunks).strip()
            else:
                response_text = str(response_gen).strip()
            if not response_text:
                rprint(f"[red]Empty response for prompt:[/red]\n{prompt}\nRetrying...")
                retries += 1
                time.sleep(2 ** retries)
        except Exception as e:
            rprint(f"[red]Error calling model:[/red] {str(e)}. Retrying in {2 ** retries} sec...")
            time.sleep(2 ** retries)
            retries += 1
    return response_text if response_text else "N/A"

# ===================== CONVERSATION GENERATION =====================
def generate_conversation(instruction: Dict) -> Dict:
    """
    Given an instruction, generate a 6-turn conversation (3 user turns, 3 assistant turns)
    with complete conversation history provided in each prompt.
    Uses separate system prompts and intros for teacher and student.
    Returns a dict formatted for a HuggingFace dataset row.
    """
    # Turn 1: User initial question.
    user_init = f"{instruction.get('instruction', '').strip()} {instruction.get('input', '').strip()}".strip()
    conversation = [{"role": "user", "content": user_init}]
    
    # Turn 2: Teacher initial response.
    t_prompt = teacher_prompt_template.format(
        user_question=user_init,
        example_output=instruction.get("output", "").strip()
    )
    teacher_initial = call_model(t_prompt, max_tokens=4096, timeout=3000,
                                 sys_prompt=teacher_system_prompt, intro=teacher_intro)
    conversation.append({"role": "assistant", "content": teacher_initial})
    
    # Turn 3: Student first follow-up question.
    history = build_conversation_history(conversation)
    student_first_prompt = history + "\nNow, " + student_followup_prompt
    student_first = call_model(student_first_prompt, max_tokens=512, timeout=2000,
                               sys_prompt=student_system_prompt, intro=student_intro)
    conversation.append({"role": "user", "content": student_first})
    
    # Turn 4: Teacher first follow-up answer.
    history = build_conversation_history(conversation)
    teacher_followup_1_prompt = history + "\n" + teacher_followup_prompt_template.format(followup_question=student_first)
    teacher_followup_1 = call_model(teacher_followup_1_prompt, max_tokens=512, timeout=3000,
                                    sys_prompt=teacher_system_prompt, intro=teacher_intro)
    conversation.append({"role": "assistant", "content": teacher_followup_1})
    
    # Turn 5: Student second follow-up question.
    history = build_conversation_history(conversation)
    student_second_prompt = history + "\nNow, " + student_followup_prompt
    student_second = call_model(student_second_prompt, max_tokens=512, timeout=2000,
                                sys_prompt=student_system_prompt, intro=student_intro)
    conversation.append({"role": "user", "content": student_second})
    
    # Turn 6: Teacher second follow-up answer.
    history = build_conversation_history(conversation)
    teacher_followup_2_prompt = history + "\n" + teacher_followup_prompt_template.format(followup_question=student_second)
    teacher_followup_2 = call_model(teacher_followup_2_prompt, max_tokens=512, timeout=3000,
                                    sys_prompt=teacher_system_prompt, intro=teacher_intro)
    conversation.append({"role": "assistant", "content": teacher_followup_2})
    
    return {
        "instruction": user_init,
        "conversation": conversation,
        "metadata": {
            "stringlengths": {
                "user_initial": len(user_init),
                "teacher_initial": len(teacher_initial),
                "student_first": len(student_first),
                "teacher_followup_1": len(teacher_followup_1),
                "student_second": len(student_second),
                "teacher_followup_2": len(teacher_followup_2)
            },
            "timestamp": datetime.datetime.now().isoformat()
        }
    }

# ===================== CHUNK PROCESSING FUNCTIONS =====================
def process_chunk(file_path: str, output_path: str) -> None:
    """
    Process a chunk file (a JSON list of instructions) to generate a conversation dataset.
    Writes the resulting HuggingFace-format dataset JSON using DatasetBuilder.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            instructions = json.load(f)
        
        dataset_rows = []
        for instruction in instructions:
            row = generate_conversation(instruction)
            dataset_rows.append(row)
            time.sleep(1)  # Delay between generations to avoid rate limits
        
        ds_builder = DatasetBuilder(output_path)
        for row in dataset_rows:
            ds_builder.add_datapoint_from_json(row)
        
        rprint(f"[green]Processed chunk saved to:[/green] {output_path}")
    except Exception as e:
        rprint(f"[red]Error processing chunk {file_path}: {str(e)}[/red]")

def split_input_file(input_file: str, num_chunks: int) -> List[str]:
    """
    Split the input JSON file into several chunk files.
    Returns a list of chunk file paths.
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        rprint(f"[red]Error reading {input_file}: {str(e)}[/red]")
        return []
    
    total = len(data)
    chunk_size = max(1, total // num_chunks)
    os.makedirs("chunks", exist_ok=True)
    chunk_files = []
    for i in range(0, total, chunk_size):
        chunk = data[i:i + chunk_size]
        chunk_file = os.path.join("chunks", f"chunk_{i//chunk_size}.json")
        with open(chunk_file, 'w', encoding='utf-8') as f:
            json.dump(chunk, f, ensure_ascii=False, indent=2)
        chunk_files.append(chunk_file)
    return chunk_files

def merge_chunks(chunk_output_files: List[str], final_output_file: str) -> None:
    """
    Merge all processed chunk files into a final dataset file formatted for HuggingFace.
    """
    merged = []
    for file in chunk_output_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                rows = json.load(f)
                merged.extend(rows)
        except Exception as e:
            rprint(f"[red]Error merging file {file}: {str(e)}[/red]")
    
    final_ds = DatasetBuilder(final_output_file)
    for row in merged:
        final_ds.add_datapoint_from_json(row)
    rprint(f"[green]Final merged dataset saved to:[/green] {final_output_file}")

# ===================== MAIN FUNCTION =====================
def main(input_file: str, output_folder: str, final_output_file: str, num_chunks: int = 5) -> None:
    os.makedirs(output_folder, exist_ok=True)
    chunk_files = split_input_file(input_file, num_chunks)
    
    processed_chunk_files = []
    threads = []
    
    for chunk_file in chunk_files:
        output_path = os.path.join(output_folder, f"processed_{os.path.basename(chunk_file)}")
        processed_chunk_files.append(output_path)
        t = threading.Thread(target=process_chunk, args=(chunk_file, output_path))
        t.start()
        threads.append(t)
    
    for t in threads:
        t.join()
    
    merge_chunks(processed_chunk_files, final_output_file)

if __name__ == "__main__":
    # Define file paths and parameters.
    input_file = r"1.1.json"  # Input file with instructions (JSON format)
    output_folder = r"processed_chunks"  # Directory to store individual processed chunk outputs.
    final_output_file = r"hf_conversation_dataset.json"  # Final merged HuggingFace dataset file.
    
    # Launch the main processing function with specified number of chunks.
    main(input_file, output_folder, final_output_file, num_chunks=10)
