[{"instruction": "बताएं कि हाथ से लिखे शब्दों को पहचानने के लिए एक न्यूरल नेटवर्क का उपयोग कैसे किया जा सकता है।\n"}, {"instruction": "डिजाइन के दो तत्वों का नाम बताएं।\n"}, {"instruction": "पासाज को एक और सक्रिय और संक्षिप्त संस्करण में बदलें।\nजॉन के पास एक विचार था जिसे वह अपनी दोस्त रेचल को संचारित करना चाहता था। इसके लिए, वह उसे इस तरीके से समझाने की आवश्यकता थी कि वह इसे समझ सके।\n\nपासाज को एक और सक्रिय और संक्षिप्त संस्करण में बदलें।"}, {"instruction": "\"सूरज पश्चिम में अस्त हो रहा था\" ये वाक्य सुरु करती हुई एक रचनात्मक कहानी लिखो।\n"}, {"instruction": "दो संगठनों का नाम बताइए जो बे घर लोगों की मदद करते हैं।\n"}, {"instruction": "360° प्रतिक्रिया की अवधारणा वर्णन करें।\n"}, {"instruction": "एक विशेष भावना को दर्शाने वाली संभवतः वाक्य बनाएं।\n"}, {"instruction": "जावा में निम्नलिखित कार्यक्रम कंपाइल करें।\nजावा में निम्नलिखित कार्यक्रम कंपाइल करें।\n\nक्लास HelloWorld {\n   सार्वजनिक स्थानिक void main(String[] args) {\n      System.out.println(\"हैलो वर्ल्ड!\");\n   }\n}"}, {"instruction": "नीचे दिए गए निर्देशों का पालन करके इस कैबिनेट को असेंबल करें।\n![कैबिनेट](https://i.etsystatic.com/11764299/r/il/b9a7e5/1463799956/il_fullxfull.1463799956_sg1v.jpg)"}, {"instruction": "बच्चों के लिए मानसिक स्वास्थ्य पर उचित पाठ तैयार करें।\n"}, {"instruction": "दो चरित्रों का परिचय देने के लिए एक संवाद बनाएँ।\nहैरी और मैरी"}, {"instruction": "3 महीनों में एक नई कौशल सीखने के लिए एक योजना तैयार करें।\n"}, {"instruction": "दिए गए विवरण का उपयोग करके एक नया प्रकार का राक्षस उत्पन्न करें।\nविवरण: बालू और समुद्र शंखों के एक राक्षस का निर्माण किया।"}, {"instruction": "दिए गए अनुच्छेद में तर्क का मूल्यांकन करें।\nआज की आधुनिक दुनिया में, सार्वजनिक शिक्षा को हर किसी के लिए मुफ्त होना चाहिए, उनके समाज-आर्थिक स्थिति के बावजूद। यदि कुछ लोग धनवान होते हैं, तो भी सभी को एक समान शिक्षा का अवसर मिलना चाहिए।"}, {"instruction": "एक स्थिति का उदाहरण दें जिसमें एक उपसर्ग की आवश्यकता होगी ताकि एक शब्द का सही रूप से उपयोग किया जा सके।\n"}, {"instruction": "एक डेटा सेट दिया गया होने पर, मुख्य फिंडिंग्स को उजागर करने वाली रिपोर्ट तैयार करें।\nकंपनी ने अपने उत्पादों से जुड़े अपने 1000 ग्राहकों से उनकी खुशी के बारे में सर्वेक्षण किया। परिणाम निम्नलिखित हैं: 25% ने कहा कि वे बहुत खुश हैं, 40% ने कहा कि वे थोड़े खुश हैं, 20% ने कहा कि वे न खुश हैं न ही नाराज़, 11% ने कहा कि उन्हें थोड़ी नाराज़गी होती है और 4% ने कहा कि वे बहुत नाराज़ हैं।"}, {"instruction": "नीचे दिए गए शब्दों को एक सार्थक क्रम में व्यवस्थित करें।\nचाँद तारे रात चमकते हुए"}, {"instruction": "निम्नलिखित खेल जानकारी दी गई है, उसके लिए एक उपयुक्त खेल सामग्री उत्पन्न करें।\nशैली: साहसिक \nस्थापना: एक जादुई दुनिया"}, {"instruction": "ऐसी सुची तैयार करें जिसमें टीनेजर्स कोडिंग से जुड़े रहने वाले उन 5 किताबों की जानकारी हो जो उन्हें पढ़ना चाहिए।\n"}, {"instruction": "इस वाक्य को अलग-अलग शब्दों में पुनर्रचित करें।\nदो विकल्पों के बीच विकल्प चुना गया था।"}, {"instruction": "एक मोमबत्ती और एक नदी के बीच तुलना बनाएं।\nकोई इनपुट नहीं।"}, {"instruction": "एक प्रोग्रामिंग पद के लिए आवेदन करने के लिए एक व्यक्ति द्वारा उपयोग किया जा सकने वाले एक रिज्यूम उदाहरण बनाएं।\n"}, {"instruction": "किसी छोटे व्यवसाय को बताएं कि वह अपनी मार्केटिंग रणनीति को कैसे प्रचारित कर सकता है।\n"}, {"instruction": "एक दोस्त से पूछने के लिए प्रश्नों का एक समूह उत्पन्न करें।\n"}, {"instruction": "किसी व्यक्ति कैसे एक स्वस्थ जीवनशैली विकसित कर सकता है?\n"}, {"instruction": "निम्नलिखित सैल्सियस तापमान को फारेनहाइट में रूपांतरित करें और उत्तर को दो दशमलव स्थानों तक आसानी से पहुंचाएँ।\n84.2°F"}, {"instruction": "2019 से 2020 तक अमेरिका में रोजगार दर दिखाने के लिए एक चार्ट बनाएं।\n"}, {"instruction": "बादल और हवा तापमान के बीच संबंध क्या है?\n"}, {"instruction": "कुछ एक रीसाइक्लिंग अभियान के लिए एक नारा सुझाव दें।\n"}, {"instruction": "Apache Cassandra और Apache HBase के डेटा आर्किटेक्चर को तुलना और विस्तार से तुलना करें।\n"}, {"instruction": "एक टी-शर्ट के लिए एक डिजाइन बनाएं जो निम्नलिखित दो शब्दों - साहसिक यात्रा और रहस्य - को शामिल करता हो।\n"}, {"instruction": "कृपया बताएं कि दी गई विधिमें से निष्कर्ष निकाला जा सकता है।\nशहर में नागरिकों की संख्या पिछले दो सालों में काफी तेजी से बढ़ गई है।"}, {"instruction": "हर हफ्ते का खाने का योजना तैयार करें, जो महाजनात्मक अंकों के साथ भोजनों को शामिल करता है जो 2000 कैलोरी हो।\n"}, {"instruction": "एक नई दांतों के लिए ब्रांड के लिए विज्ञापन के लिए एक पाठ लिखें।\n"}, {"instruction": "एक चैरिटी मैराथन में भाग लेने के लिए एक नियम सेट तैयार करें।\n"}, {"instruction": "टेक्नोलॉजी में करियर चुनने के दो फायदे सूचीबद्ध करें।\n"}, {"instruction": "फजीतास के महत्वपूर्ण घटक क्या हैं?\n"}, {"instruction": "वाक्य में एक सामान्य गलती की पहचान करें और सुधार प्रदान करें।\nमैंने उस फिल्म को देखा है।"}, {"instruction": "अमेरिकी राष्ट्रीय झंडे का वर्णन करें।\n"}, {"instruction": "एक प्रभावी प्रबंधक बनने के लिए आवश्यक कौशल सूची बनाएं।\n"}, {"instruction": "एक नई अभिनव प्रौद्योगिकी का वर्णन करें।\n"}, {"instruction": "AI एप्लिकेशन विकसित करने की मुख्य चुनौतियों की पहचान करें।\n"}, {"instruction": "एक प्रभावी प्रस्तुति के चार घटकों का वर्णन करें।\n"}, {"instruction": "आप एक ग्राहक की समस्या का सम्मानपूर्ण और सहानुभूति से संबोधित करेंगे?\n"}, {"instruction": "एक जीपीटी मॉडल के लिए एक कार्य बनाएं।\n"}, {"instruction": "पाँच अलग-अलग प्रोग्रामिंग भाषाओं का नाम बताएँ।\n"}, {"instruction": "साहित्य में विभिन्न टोन का वर्णन करें।\n"}, {"instruction": "एक स्वस्थ जीवन जीने के पांच रोचक कारणों की सूची तैयार करें।\n"}, {"instruction": "एक विशिष्ट पुस्तक के विषय में वर्णन करें।\nकैचर इन द राई"}, {"instruction": "AI को समझने के लिए\nतिन-पंक्ति की एक कविता लिखें\nताकि हो जाए सब जानते हुए AI के बारे में।\n"}, {"instruction": "लोग अपने करियर के उद्देश्य का पता लगाने के लिए अपने आप से पूछेंगे पाँच सवाल जेनेरेट करें।\n"}, {"instruction": "मैथेन की आणविक संरचना का वर्णन करें।\n"}, {"instruction": "बताएं कि सहायता में रहना क्यों महत्वपूर्ण है।\n"}, {"instruction": "एक स्थिति का वर्णन करें जहाँ एक चैटबॉट एक मानव सहायक से अधिक उपयोगी होगा।\n"}, {"instruction": "एक ऐप बताएं जो लोगों को ऊर्जा की खपत कम करने में मदद कर सकता है।\n"}, {"instruction": "एक संयुक्त शौक के बारे में बातचीत करने वाले दो लोगों के लिए कुछ बातचीत के प्रोम्प्ट उत्पन्न करें।\n"}, {"instruction": "दिए गए निबंध में संदेह उत्पन्न करने के लिए उसे संशोधित करें।\nमुझे लगता है कि स्कूल एक सुरक्षित वातावरण होना चाहिए जहाँ छात्र सीख सकते हैं और विकसित हो सकते हैं। स्कूल एक ऐसी जगह होनी चाहिए जहाँ छात्र सुरक्षित और सुरक्षित महसूस करते हैं और कम विघटन के साथ ध्यान केंद्रित कर सकते हैं।"}, {"instruction": "दिए गए विषय सूची के बारे में एक लेख के लिए शीर्षक बनाएं।\nपकाना, टिकाऊता और डिजिटल प्रौद्योगिकी"}, {"instruction": "0-5 के मात्रात्मक स्केल का उपयोग करके निम्नलिखित निबंध को ग्रेड करें।\nउच्च शिक्षा के बढ़ते खर्च ने कई छात्रों के लिए कॉलेज को पहुंच नामुमकिन बना दिया है। अधिक से अधिक छात्र अपने खर्चों को कवर करने के लिए ऋण ले रहे हैं और उच्च शिक्षा से निकलकर उच्च राशि के छात्र ऋण के साथ स्नातक हो रहे हैं।"}, {"instruction": "Python से प्रोग्रामिंग के लिए सर्वश्रेष्ठ अभ्यासों पर चर्चा करने वाली 3 वेबसाइट ढूंढें।\n"}, {"instruction": "विफलता हमें बेहतर बनने में कैसे मदद कर सकती है?\n"}, {"instruction": "एक आयाम की परिधि की गणना करने के लिए एक एल्गोरिथम लिखें।\n"}, {"instruction": "साहस की परिभाषा बताएँ।\n"}, {"instruction": "मृत्यु दंड का उपयोग क्यों नहीं किया जाना चाहिए, इसके बारे में एक तर्क प्रदान करें।\n"}, {"instruction": "फास्ट फूड खाने के तीन फायदे और तीन नुकसान पहचानें।\n"}, {"instruction": "कॉमिक बुक श्रृंखला के लिए एक टैग लाइन बनाएं।\n"}, {"instruction": "जॉगिंग के लिए एक वैकल्पिक व्यायाम सुझाएं।\n"}, {"instruction": "एक गैर-उपन्यास बुक का शीर्षक और उसकी शैली दी गई है, वेब पर बुक की सारांश खोजें और उसे अपने शब्दों में लिखें।\nपुस्तक का शीर्षक: बड़े सोचने का जादू\nजानर: निजी विकास"}, {"instruction": "एक व्यक्तिगत घटना का विवरण देकर हाइकू शैली में एक छंद लिखें।\nधूपवाले दिन समुद्र में तैरना।"}, {"instruction": "एक पूर्ण उतोपिया समाज में दुनिया कैसी होगी, इसे वर्णित करें।\n"}, {"instruction": "अमेरिका में दस बड़ी कंपनियों की सूची बनाएं।\n"}, {"instruction": "बोटाई कैसे बांधना है, वह बताएँ।\n"}, {"instruction": "एक व्यावसायिक ग्राफ़िक डिजाइनर के जीवन का एक सामान्य दिन सारांशित करें।\n"}, {"instruction": "इस अंश में लेखक के भाव की पहचान करें।\nयह हमारे लिए एक महान अवसर है जहाँ हम सभी टीम के साथ एक साथ आकर अपनी चतुराई का प्रदर्शन कर सकते हैं।"}, {"instruction": "पहली बार धरती पर आये एक एलियन के बारे में एक कहानी लिखें।\n"}, {"instruction": "निम्नलिखित शब्दों को तीन अलग-अलग समूहों में वर्गीकृत करें।\nविश्वसनीय, प्रेत, सम्मान्"}, {"instruction": "एक व्यक्ति के कोलेस्ट्रॉल स्तर को कम करने के लिए उनके लिए आहार में परिवर्तनों की सलाह दें।\n"}, {"instruction": "नई कार चमकदार है और रंग-बिरंगी है, जिसमें एक शानदार साउंड सिस्टम है।\n"}, {"instruction": "एक नौकरी विज्ञापन दिया गया है, सबसे प्रभावी क्रम में भागों को व्यवस्थित करें।\nपूर्णकालिक नौकरी\nप्रतिस्पर्धी वेतन\nकंप्यूटर कौशल आवश्यक हैं"}, {"instruction": "दिए गए एरे से डुप्लिकेट को हटा दें।\nएरे: [3, 5, 8, 8, 9, 5, 4]\n\nदिए गए एरे से डुप्लिकेट को हटा दें।"}, {"instruction": "मानव खाद्य की पांच अलग स्वाद क्या हैं?\n"}, {"instruction": "पर्यावरण संरक्षण के लाभों का वर्णन करें।\n"}, {"instruction": "एक त्रिकोण के क्षेत्र दिए गए होने पर उसका आधार और ऊंचाई निर्धारित करें।\nक्षेत्रफल: 20 सेमी के वर्गान्त मीटर्।"}, {"instruction": "बताएं कि पूर्वी द्वीप पर मोआई मूर्तियों के शरीर नहीं होते हैं।\n"}, {"instruction": "\"वह हाथ काटता है जो उसे चराया देता है\" वाक्यार्थ का विवरण दें।\n"}, {"instruction": "इस मुद्दे के बारे में एक भविष्यवाणी बनाएंगे कि यह कैसे समाप्त होगा।\nदो देश अपने व्यापार समझौते पर दो - बेईमानी में बंद हैं।"}, {"instruction": "एक विशिष्ट अर्थ वाला 5 शब्दों का वाक्य उत्पन्न करें।\n"}, {"instruction": "दिए गए वाक्य में भाषण शैली की पहचान करें।\nवह दुकान गई।"}, {"instruction": "एक आंतरिक हार्ड ड्राइव और एक बाहरी हार्ड ड्राइव के बीच अंतर क्या है?\n"}, {"instruction": "समाज में एक वर्तमान मुद्दे का सामना करने वाले एक आविष्कार का अभिप्राय बनाएं।\n"}, {"instruction": "343⁷ के अंतिम तीन अंक ढूंढें।\n"}, {"instruction": "दो चरित्रों के बीच एक असहमति का उदाहरण देने वाली बातचीत बनाएं।\n"}, {"instruction": "अमेरिका में उर्जा संचय के प्रकारों का विवरण दें।\n"}, {"instruction": "Google की वर्तमान आय वृद्धि दर की पहचान करें।\n"}, {"instruction": "ऑनलाइन सुरक्षा को बेहतर करने के लिए समाधानों की एक सूची बनाएं।\n"}, {"instruction": "Java क्लास बनाएँ जिसमें तीन मेथड्स हों।\n"}, {"instruction": "कारण-परिणाम संबंध के एक उचित उदाहरण बनाएं।\n"}, {"instruction": "दिए गए त्रिभुज के कोणों के माप निर्धारित करें।\nदी गई तिकोनीय तरफों की लंबाई: 14, 3, 15"}, {"instruction": "किसी अधिक भीड़ भारी शहर में रहने के संभव नतीजों का वर्णन करें।\n"}, {"instruction": "19वीं सदी की तीन महत्वपूर्ण वैज्ञानिक उपलब्धियों की पहचान करें।\n"}, {"instruction": "एक वर्तमान घटना दी गई है, उस घटना के परिणामों को समझाएं।\nचाइना का क्षेत्रविस्तार"}, {"instruction": "निम्नलिखित बयान को संपादित करें: 'हर कोई विशेष होता है।'\n"}, {"instruction": "कॉपीराइट कानून के पीछे कौन सा कानूनी सिद्धांत है?\n"}, {"instruction": "फोटोसिंथेसिस में प्रकाश की भूमिका को वर्णित करें।\n"}, {"instruction": "कार्यस्थल में \"विविधता\" विषय का उपयोग करके एक पोस्टर डिजाइन करें।\n"}, {"instruction": "निम्नलिखित सूची के 3 और 7 वाँ तत्व निकालें:\n[1, 5, 8, 11, 15, 20, 24, 30] के 3 और 7 वाँ तत्व निकालें:"}, {"instruction": "अपने बच्चों के लिए पिता आमतौर पर कुछ ऐसा करने का उदाहरण दें।\n"}, {"instruction": "एक बड़ी कंपनी को छोटा करने के लिए सबसे अच्छा पहलु की भविष्यवाणी करें।\n"}, {"instruction": "इस वाक्य को संशोधित करें ताकि इसे अधिक प्रभावी बनाया जा सके: \"यह उत्पाद अपनी श्रेणी में सर्वश्रेष्ठ है।\"\nइस उत्पाद ने अपनी श्रेणी में श्रेष्ठता की शिखर पर पहुंचा है।"}, {"instruction": "बच्चों के लिए पाठ्यक्रम के अनुरूप मजेदार विचित्र नुक्तेबाजी का वर्णन करें।\n"}, {"instruction": "कन्वोल्यूशनल न्यूरल नेटवर्क में \"पूलिंग लेयर\" की अवधारणा का विवरण दीजिए।\n"}, {"instruction": "इन शब्दों को वर्णमाला के क्रमानुसार व्यवस्थित करें: quickly, question, queue, quality.\n"}, {"instruction": "एक URL दिया गया है, जिसकी सामग्री का सारांश तैयार करें।\nhttps://www.nytimes.com/2020/09/02/business/stock-market-today.html"}, {"instruction": "एक पाठ दिया गया है, उसके लिए एक उपयुक्त विषय सुझाएं।\nयह एक कहानी है जो एक असामान्य दिन के बारे में है जब एक अजीब मेहमान एक छोटे से गांव में पहुँचा।"}, {"instruction": "उन लोगों का नाम बताएं जो दिए गए क्षेत्र में प्रभावशाली रहे हैं।\nरोबोटिक्स।"}, {"instruction": "\"माइक्रोसर्विसेस\" शब्द के लिए परिणाम दिखाने वाला एक खोज इंजन क्वेरी बनाएँ।\n"}, {"instruction": "मेशीन लर्निंग में प्रयुक्त मुख्य चरण क्या हैं?\n"}, {"instruction": "आपको एक कंपनी का नाम और एक स्लोगन दिया गया है। कंपनी के नाम और स्लोगन को सम्मिलित करने वाले एक लोगो डिजाइन बनाएं।\nकंपनी का नाम: रॉयल एक्सप्रेस\nस्लोगन: भरोसेमंद वितरण समाधान"}, {"instruction": "उन्हें कौन सा रंग पहना हुआ होगा, यह पूर्वानुमान लगाएँ।\nवह आदमी चमकदार आभूषणों वाली उच्च कॉलर वाली पोशाक में थे।"}, {"instruction": "असंचालित प्रणाली को सुधारने में शामिल चरणों की व्याख्या करें।\n"}, {"instruction": "निम्नलिखित वाक्य को लेकर एक व्यक्ति का विवरण बनाएँ।\nवह भौतिक विज्ञान की अध्ययन करता है।"}, {"instruction": "\"सेवक नेतृत्व\" की अवधारणा का विवरण दो।\n"}, {"instruction": "एक मध्ययुगीन क़िला के दरवाजे का डिज़ाइन करें।\n"}, {"instruction": "पानी की बख्ता कम करने के दो तरीके सुझाएँ।\n"}, {"instruction": "नीचे दिए गए कोड को पुन: फिर से उर्ध्वगामी इस्तेमाल करने के लिए लिखें।\nफैक्टोरियल(n) फ़ंक्शन:\n    परिणाम = 1\n    बाएं से दाएं तक के लिए i में रेंज(1, n + 1):\n        परिणाम *= i\n    वापस परिणाम"}, {"instruction": "एक बगीचे में चलते हुए एक बिल्ली का विवरण उत्पन्न करें।\n"}, {"instruction": "अंग्रेजी में एक विख्यात उद्धरण का उदाहरण दें।\n"}, {"instruction": "\"अंधेरे में जब से ऊर्जा की किरणें दिखाई नहीं देतीं, मनुष्यों ने सूर्य तक पहुंचने का निरंतर प्रयास किया है।\"\n"}, {"instruction": "उच्च गति इंटरनेट का वर्णन करने वाली एक रूपक लिखें।\n"}, {"instruction": "अंतरिक्ष में एक उद्धार मिशन का विवरण दें।\n"}, {"instruction": "निम्नलिखित प्रसिद्ध व्यक्ति को सबसे उपयुक्त लेबल के साथ वर्गीकृत करें।\nबिल गेट्स"}, {"instruction": "\"हमें एक योजना विकसित करने की आवश्यकता है\" शब्दों के पुनरावर्तन से बचने के लिए इस वाक्य को फिर से शब्दों को दोहराना करने के बिना परिवर्तित करें।\n"}, {"instruction": "दो बिंदुओं के बीच कोण ढूंढें।\nA(3,1) और B(5,3)"}, {"instruction": "एक सप्ताह के भोजन के लिए एक ग्रोसरी सूची बनाएं।\nसप्ताह के भोजन: स्पैगेटी, टैको, ग्रिल्ड चिकन, स्टिर-फ्राई।"}, {"instruction": "एक काल्पनिक प्राणी बनाएं और उसका विस्तार से विवरण दें।\n"}, {"instruction": "दिए गए इनपुट का उपयोग करके, सपनों से संबंधित एक कहानी बनाएं।\nवृद्ध आदमी अकेले गलियों में भटक रहा था।"}, {"instruction": "निम्नलिखित वाक्य में \"बढ़िया\" शब्द की जगह कम उपयोग में आने वाले कोई और शब्द सुझाएं:\nउसका दिन बहुत अच्छा था।"}, {"instruction": "\"The man in the blue shirt walked across the street.\" निम्नलिखित को श्रेणिबद्ध करें:\n"}, {"instruction": "एक अंडे को उबालने की प्रक्रिया का वर्णन करें।\n"}, {"instruction": "उत्तर दें: जलावर्ती औरके अलावा विद्युत उत्पादन करने के वैकल्पिक तरीके क्या हैं?\n"}, {"instruction": "विवाद समाधान में उपयोग किए जाने वाले दो संचार के तरीकों का नाम बताएं।\n"}, {"instruction": "दो वाक्यों को दिए गए हैं, दो वाक्यों पर आधारित आई एक तीसरा वाक्य बनाएँ।\nमैंने पार्क में उसे देखा। वह एक अजनबी से बात कर रहा था।"}, {"instruction": "कपड़ों की दुकान बेचने वाली एक ऑनलाइन शॉप डिजाइन करें।\n"}, {"instruction": "दिए गए तत्वों की सूची के आधार पर, दूसरों के लिए एक का चयन करें: मक्खन, शॉर्टनिंग, क्रीम चीज।\n"}, {"instruction": "कृपया प्लास्टिक प्रदूषण के बारे में एक शोधपत्र के लिए एक शीर्षक तैयार करें।\n"}, {"instruction": "नए उपयोगकर्ताओं को लक्षित करने के लिए एक मार्केटिंग योजना बनाएं।\n"}, {"instruction": "दिए गए शब्दों को एक कम्पाउंड संज्ञा में वर्गीकृत करें।\nसमुद्र, गील्ली"}, {"instruction": "दो मैट्रिक्स को घटाने के लिए एक फ़ंक्शन बनाएं।\n"}, {"instruction": "एक पूर्णांक के लिए जाँच करने के लिए एक फ़ंक्शन लिखें कि यह एक मौजूदा संख्या है या नहीं।\n"}, {"instruction": "इस वाक्य में कौनसा साहित्यिक उपकरण प्रयोग किया जा रहा है?\nउनकी आँखों में खुशी सफलता देने वाली हानिकारक जगहों को देखकर चमक उठी।"}, {"instruction": "मुझे बताओ कि origami हंस कैसे बनाया जाता है।\n"}, {"instruction": "Tic-tac-toe के एक वेब संस्करण का डिज़ाइन बनाएं।\n"}, {"instruction": "एक सप्ताह की छुट्टियों के लिए एक ऐसा स्थान अमेरिका में ढूंढें जो उपयुक्त हो।\n"}, {"instruction": "इस पद्धति में अगले 3 शब्द ढूंढें: 4, 6, 9, 12\n"}, {"instruction": "मार्चिंग बैंड के लाभों के बारे में एक राय टुकड़ा लिखें।\n"}, {"instruction": "एक गर्मी के समुद्र तट दृश्य की एक छवि / आकर्षण बनाएं।\n"}, {"instruction": "कार्बन उत्सर्जन को कम करने के लिए संभव रणनीतियों की सुझाव दीजिए।\n"}, {"instruction": "दिए गए विषय पर चर्चा में भाग लें।\nSTEM क्षेत्र में लिंग अंतर को कम करने के लिए क्या किया जा सकता है?"}, {"instruction": "क्या दो वाक्य तार्किक रूप से अनुरूप हैं?\nचाँद पनीर से बना है। \nमंगल सूरज से चौथा ग्रह है।"}, {"instruction": "जावास्क्रिप्ट में एक फ़ंक्शन लिखें जिससे वर्तमान तिथि छापी जाए।\n"}, {"instruction": "एक नवीनतम समाचार लेख के बारे में जो नवीनीकरणीय ऊर्जा के बारे में हो, उसका एक संक्षिप्त सारांश लिखें।\n"}, {"instruction": "कंप्यूटर इंजीनियरिंग में रैम और रोम के बीच अंतर क्या है?\n"}, {"instruction": "इस समाचार लेख के राजनीतिक परिणामों का विश्लेषण करें।\nप्रथम नवंबर को राष्ट्रपति ने एक योजना की घोषणा की है जिसमें रक्षा के लिए बजट को 10% बढ़ाने का अनुरोध किया गया है, अगले 6 साल में 1 ट्रिलियन डॉलर से अधिक का जमा होगा।"}, {"instruction": "कोई समुच्चय बिना किसी जोड़ के इन दो वाक्यों को मिलाएँ।\nवह गहरी नींद में सो गया। उसने बिजली की गरज को नहीं सुना।"}, {"instruction": "बताएं कि एक सफल ग्राहक आकर्षण रणनीति कैसी दिखती है।\n"}, {"instruction": "प्रबंधन कार्य के लिए एक कर्मचारी के पास कौन से तीन कौशल होने चाहिए?\n"}, {"instruction": "यह बताइए कि दिए गए ट्विटर पोस्ट में विरोधात्मक भाषा है या नहीं।\nकुछ लोगों को यह समझने की जरूरत है कि उनकी राय मजबूत नहीं होती।"}, {"instruction": "दिए गए समस्या के लिए सॉफ्टवेयर समाधान प्रदान करें।\nकंपनी XYZ अपनी इन्वेंटरी और आर्डर को संचालित करने के लिए एक सॉफ्टवेयर समाधान की तलाश में है।"}, {"instruction": "समाजी दूरी बढ़ाने के लिए ट्वीट लिखें।\n"}, {"instruction": "बिस्कुट पकाने के लिए ओवन की औसत तापमान ढूंढें।\n"}, {"instruction": "गरम हवा बैलून का उपयोग परिवहन के लिए कुछ फायदे और हानियों की सूची दें।\n"}, {"instruction": "दर्शाओ कि उपयोगकर्ताओं को कुछ डेटा तक पहुंचने से पहले अनुमतियाँ क्यों चाहिए।\n"}, {"instruction": "दो वाक्यों को दिए गए हैं, उन्हें एक उपमा का उपयोग करके फिर से लिखें।\nवह बहुत मददगार थी।\nवह अत्यंत धैर्यवान थे।"}, {"instruction": "दी गई समाचार लेखन की श्रेणी तय करें\n\nसमाचार लेख: \n\nमंगलवार को Apple Inc. ने अपने वित्तीय तीसरे क्वार्टर में अपेक्षाकृत से अधिक मजबूत आंकड़े दिए और अपने डिविडेंड बढ़ाने की योजना घोषित की।\nसमाचार लेख: \nमंगलवार को Apple Inc. ने अपने वित्तीय तीसरे क्वार्टर में अपेक्षाकृत से अधिक मजबूत आंकड़े दिए और अपने डिविडेंड बढ़ाने की योजना घोषित की।"}, {"instruction": "बताएं कि दिए गए समस्या के लिए कौन सा सबसे उपयुक्त मशीन लर्निंग एल्गोरिथम उपयोग किया जा सकता है।\nविमान के लिए उड़ान के समय का गणना करें।"}, {"instruction": "एक प्रोटीन से भरपूर खाद्य विकल्प का नाम बताइए।\n"}, {"instruction": "वर्णन करें कि स्वास्थ्य उद्योग में इंटरनेट ऑफ थिंग्स का उपयोग कैसे किया जाता है।\n"}, {"instruction": "दिए गए उत्पाद के लिए ग्राहक संतुष्टि स्तर निर्धारित करने के लिए एक सर्वेक्षण डिजाइन करें।\n"}, {"instruction": "ऑस्ट्रेलियाई आउटबैक में कौन से दो पक्षी आमतौर पर देखे जाते हैं?\n"}, {"instruction": "\"जलवायु परिवर्तन एक गंभीर मुद्दा बन रहा है।\" - जलवायु परिवर्तन के प्रभावों को पेश करने के लिए निम्नलिखित वाक्य को पुनः लिखें।\n"}, {"instruction": "मेरे साथ सहयोग करें और निम्नलिखित समस्या का समाधान खोजें।\nग्रोसरी स्टोर में इस्तेमाल की जाने वाली प्लास्टिक बैगों की संख्या को कम करने के लिए कैसे करें?"}, {"instruction": "एक उत्पाद की विशेषता के बारे में एक बॉट से पूछे जाने वाले कुछ प्रश्नों की एक श्रृंखला बनाएं।\n"}, {"instruction": "सूरज की एक विशिष्ट विशेषता का वर्णन करें।\n"}, {"instruction": "निम्नलिखित बयान दिया गया है, जिसके अनुसार इस का उपयोग किसी वाक्य में उदाहरण दें।\nइसके अलावा"}, {"instruction": "मनोवैज्ञानिक विकार का नाम बताएं।\n"}, {"instruction": "दिये गए प्रोग्राम को जावा में दोबारा लिखें।\na = 4\nb = 5\nc = a + b\nprint(c)"}, {"instruction": "एक इनपुट वाक्य लें और उसे पैसिव वॉइस में बदलें।\nहम रात का खाना पका रहे हैं।"}, {"instruction": "1 से 10 के मापदंड पर निम्नलिखित वक्तव्य का मूल्यांकन करें, जहाँ एक सबसे कम सटीकता होता है, और दस सबसे अधिक सटीक होता है।\nसामाजिक मीडिया दुनिया भर में लोगों को जोड़ने के लिए एक शक्तिशाली उपकरण है।"}, {"instruction": "विभिन्न वर्गों की एक विविध कार्यबल बनाना क्यों महत्वपूर्ण है, इसे समझाएं।\n"}, {"instruction": "ज शॉशंक रेडम्पशन के लिए एक फिल्म समीक्षा लिखें।\n"}, {"instruction": "\"पूर्ववत का एक उदाहरण वाक्य दें जो 'precocious' शब्द का प्रयोग करता हो।\"\n"}, {"instruction": "आप बड़े समूह को कैसे अभिवादन करेंगे?\n"}, {"instruction": "बड़ी मात्रा में डेटा रखने और उधारण करने के लिए एक सिस्टम डिजाइन करें।\n"}, {"instruction": "सबसे स्वादिष्ट टैको बनाने का एक तरीका वर्णन करें।\n"}, {"instruction": "निम्नलिखित त्रिभुज को एक दायाँ त्रिभुज, एक तीव्र त्रिभुज या एक ऑब्ट्यूस त्रिभुज के रूप में वर्गीकृत करें।\nत्रिभुज के तीनों भुजों की लंबाई 3, 4 और 5 है।"}, {"instruction": "प्रदान किए गए सेट के बीच के संबंध का विवरण देने वाले मॉडल का निर्माण करें।\n"}, {"instruction": "एक अच्छे नेता के पांच सकारात्मक गुणों की सूची तैयार करें।\n"}, {"instruction": "निम्नलिखित वाक्य के शैली को और औपचारिक बनाएँ।\nहाय सभी, मेरे पास एक सवाल है।"}, {"instruction": "एक प्रोग्राम बनाएं जो दो छोटे टेक्स्ट के बीच समानताएं खोजेगा।\nपाठ 1: मैंने उसे जवाब देते हुए वाला टेलीफोन जोरदार बजते हुए सुना।\nपाठ 2: मैंने बज रहे फोन को उठाया।"}, {"instruction": "\"North Wind Technologies\" के लिए एक लोगो लिखें।\n"}, {"instruction": "जब वोल्फ दल में होते हैं, उन्हें कौनसे व्यवहार करते हुए देखा गया है?\n"}, {"instruction": "एक आहार में ब्लूबेरी जोड़ने के आहारिक लाभ सारग्रहित करें।\n"}, {"instruction": "वर्चुअल रिऐलिटी के अवधारणा को समझाएं।\n"}, {"instruction": "नीचे दिए गए समस्या के लिए एक वैकल्पिक समाधान उपलब्ध कराएं।\nयह रेस्तरां उन ग्राहकों की पहचान के लिए एक समाधान की आवश्यकता है जो पिछले दो हफ्तों में तीन बार से अधिक भोजन कर चुके हैं।"}, {"instruction": "एक न्यूज़ रिपोर्ट के लिए एक स्क्रिप्ट बनाएं जो शैक्षणिक प्रणाली पर कोरोनावायरस के प्रभाव पर हो।\n"}, {"instruction": "निम्नलिखित प्राकृतिक भाषा के क्वेरी को एक एसक्यूएल क्वेरी में पार्स करें।\n‘CS’ विभाग में पढ़ाने वाले अध्यापकों के नाम क्या हैं?\nदो टेबल दिए गए हैं: अध्यापक, विभाग।"}, {"instruction": "एक मोबाइल एप्लिकेशन का मुख्य उद्देश्य एक वाक्य में वर्णन करें।\n"}, {"instruction": "दिए गए थीम पर आधारित एक बच्चों की कहानी बनाएँ।\nदोस्ती"}, {"instruction": "7 से विभाज्य एक 9 अंकीय संख्या क्या है?\n"}, {"instruction": "फोन-आधारित ग्राहक सहायता के फायदों का वर्णन करें।\n"}, {"instruction": "इस शब्द का पहला अक्षर बताओ।\nएक साथ"}, {"instruction": "गीत का एक विषय लिखें।\n"}, {"instruction": "टेक्स्ट-आधारित उपयोगकर्ता इंटरफेस (TUI) क्या होता है और यह कैसे काम करता है?\n"}, {"instruction": "\"विशाल\" के लिए सही समानार्थक शब्द चुनें।\n"}, {"instruction": "अगले अंश में, विशेषण को उत्साहजनक समानार्थक शब्दों में बदलें।\nउसने उबाऊ, धूसरे पार्क में घूमा।"}, {"instruction": "मुद्रा मूल्य को यूएसडी में रूपांतरित करें।\n2.30 यूरो"}, {"instruction": "एक वर्तमान घटना दी गई है, जिसका वर्णन करने के लिए एक रूपक बनाएँ।\nविश्व की सबसे बड़ी तेल कंपनी एक्सोनमोबिल हाल ही में दिवालिया घोषित की।"}, {"instruction": "इस पाठ का सारांश: \"बच्चे कौए अपने माता-पिता का अनुपालन करके उड़ना सीखते हैं और कुछ गलतियों से सीखते हैं। वे अंत में उड़ना सीख लेते हैं जब वे बार-बार अभ्यास करते हैं।\"\nयह पाठ: \"बच्चे कौए अपने माता-पिता का अनुपालन करके उड़ना सीखते हैं और कुछ गलतियों से सीखते हैं। वे अंत में उड़ना सीख लेते हैं जब वे बार-बार अभ्यास करते हैं।\""}, {"instruction": "इंजीनियर के पास होने वाले कुशलताओं की सूची बनाएँ।\n"}, {"instruction": "तीन सबसे सामान्य दीर्घकालिक रोगों की सूची बताएँ।\n"}, {"instruction": "एक छोटे से गांव में रहने वाले एक मछुआरे के बारे में एक कहानी लिखें।\n"}, {"instruction": "चीन के तीन सुरंग बांध का वर्णन करें।\n"}, {"instruction": "निम्नलिखित लेख को लीजिए, और एक कविता की तरह दुबारा लिखिए।\nग्लोबल आबादी 2050 तक 10 अरब तक पहुंचने का परिकल्पित है। इस जनसंख्या को खाद्य, ऊर्जा, घर और नौकरियों की आवश्यकता होगी। इन आवश्यकताओं को पूरा करने के लिए दुनिया में पर्याप्त संसाधन होने की आवश्यकता है, हमें यह सुनिश्चित करना होगा कि हमारे मौजूदा संसाधन समझदारी से उपयोग किए जाते हैं और नए संसाधन विकसित किए जा सकते हैं। हमें यह भी सुनिश्चित करना होगा कि लोग पर्यावरण को बचाने और उसके विकास के लिए शिक्षित होते हैं।"}, {"instruction": "एक पाँच दिन के स्कूली सप्ताह के लिए एक तार्किक तर्क बनाएँ।\n"}, {"instruction": "कंप्यूटर बनाने के लिए आपको कौन से सामग्री की आवश्यकता होगी?\n"}, {"instruction": "5 वर्ष से कम आयु के बच्चों की मृत्यु का प्रमुख कारण क्या है?\n"}, {"instruction": "\"No-go zone\" का अर्थ परिभाषित करें।\n"}, {"instruction": "\"that he wondered if it was from another planet.\"\n"}, {"instruction": "आपको कुछ वस्तुएं दी गई हैं, उसमें एक रोचक पैटर्न ढूंढें।\nसूरज, चांद, तारे"}, {"instruction": "किसी रंग को चुनें और उसके बारे में कुछ विशेषण दें।\n"}, {"instruction": "आपको दो या तीन आइटमों की एक सूची दी जाती है, जहां आपको सभी आइटमों का अंश होने वाली एक अद्वितीय कहानी या संवाद बनाना होगा।\nसेब, रोबोट"}, {"instruction": "\"Jesus Christ Superstar\" कला-संगीत का पृष्ठभूमि क्या है?\n"}, {"instruction": "एक मशीन लर्निंग के वास्तविक जीवन उपयोग का एक उदाहरण दें।\n"}, {"instruction": "दिए गए वक्तव्य के पक्ष में एक विश्वसनीय तर्क उत्पन्न करें।\nवीडियो गेम को किसी भी अन्य कला के तरीके की तरह व्यवहार किया जाना चाहिए।"}, {"instruction": "इन ऐतिहासिक व्यक्तियों को जोड़ें।\nअब्राहम लिंकन, सीटिंग बुल्‍ल"}, {"instruction": "वस्तु-विषयक प्रोग्रामिंग की चार मौलिक विशेषताओं को समझाएं।\n"}, {"instruction": "निम्नलिखित प्रौद्योगिकी का एक बुरा दोष विवरण करें।\nनैनोटेक्नोलोजी"}, {"instruction": "मोबाइल ऐप में डेटा संग्रह करने का सबसे अच्छा तरीका क्या है?\n"}, {"instruction": "इस तर्क की तार्किक त्रुटि क्या है?\nअगर वह वास्तव में मुझसे प्यार करती है, तो वह मुझे कभी नाराज नहीं करेगी।"}, {"instruction": "एक शैक्षणिक पेपर के लिए उचित बनाने के लिए पाठ को संपादित करें।\nग्लोबल वार्मिंग एक ऐसा आपदा है। अगले कुछ समय में कोई कार्यवाही न हुई तो यह हमारा अंत होगा।"}, {"instruction": "मानसिक स्वास्थ्य के रखरखाव की महत्ता को बढ़ावा देने के लिए एक नारा का उदाहरण उत्पन्न करें।\n"}, {"instruction": "ग्राहक सेवा के लिए फोन स्क्रिप्ट तैयार करें।\nकोई इनपुट नहीं"}, {"instruction": "अपने आप इस वाक्य को बोलें।\nयह मशीन सवालों का जवाब दे सकती है।"}, {"instruction": "वैश्विक उत्तापमान के पर्यावरण पर प्रभाव का विवरण दें।\n"}, {"instruction": "एक प्रसिद्ध लेखक के साथ साक्षात्कार करने के लिए एक सूची प्रश्न उत्पन्न करें।\nकोई भी पाठ नहीं।"}, {"instruction": "कार्बन ऑफसेटिंग क्या होता है, उसे संक्षेप में समझाएं।\n"}, {"instruction": "एक डॉक्टर के ऑफिस में एक रोबोट सहायक के लिए एक उपयोग मामला सुझाएं।\n"}, {"instruction": "मुझे एक कहावत दो जो आश्चर्य व्यक्त करती है।\n"}, {"instruction": "COVID-19 से संबंधित सुरक्षा उपायों का पालन करना महत्वपूर्ण है, इस पर एक लेख तैयार करें।\n"}, {"instruction": "विभिन्न प्रकार की कारों को वर्गीकृत करने के लिए एक एआई मॉडल विकसित करें।\n"}, {"instruction": "विस्तार से बताएं कि ग्राहक सेवा में एक चैटबॉट का उपयोग कैसे किया जा सकता है।\n"}, {"instruction": "निम्नलिखित डेटाफ्रेम दिए गए हैं, उन्हें दृश्यीकरण करें।\nनाम  | नौकरी | उम्र\nजॉन  | इंजीनियर | 28\nजेन  | डॉक्टर   | 25\nएलिस | शिक्षक  | 30"}, {"instruction": "निम्नलिखित व्यंजन के लिए रेसिपी बताएं।\nतम यम सूप"}, {"instruction": "'GPT' शब्दकोश से दस्तावेज़ों के लिए एक क्वेरी बनाएं।\n"}, {"instruction": "हाल की सरकारी नीति का मूल्यांकन करें।\nयूरोपीय संघ की डिजिटल सेवा अधिनियम।"}, {"instruction": "3D प्रिंटिंग से कौन से प्रोडक्ट बनाये जा सकते हैं?\n"}, {"instruction": "आइसक्रीम और जेलाटो की तुलना करें।\n"}, {"instruction": "प्लास्टिक कचरे को कम करने के लिए 4 तरीके बताओ।\n"}, {"instruction": "मार्गरेट एटवुड द्वारा लिखित एक बुक के नाम बताएं।\n"}, {"instruction": "पाँच कारण लिस्ट कीजिए जिनसे सुबह का नाश्ता करना महत्वपूर्ण होता है।\n"}, {"instruction": "एक चैटबॉट बनाने के लिए चरणों की सूची बनाएं।\n"}, {"instruction": "कार में उपयोग किए जाने वाले इंजन और विमान में उपयोग किए जाने वाले इंजन के मुख्य अंतर का वर्णन करें।\n"}, {"instruction": "पारंपरिक शिक्षा प्रणाली के पांच समस्याओं का विचार करें।\n"}, {"instruction": "बताएं कि क्यों उबलते हुए पानी ठंडे पानी से जल्दी जमता है।\n"}, {"instruction": "एक देश के नाम के आधार पर, एक पर्यटक स्थल उत्पन्न करें।\nफिनलैंड"}, {"instruction": "AI का उपयोग करने में एक चुनौती की पहचान करें।\n"}, {"instruction": "\"दिल और आत्मा\" का मुहावरे का अर्थ समझाएं।\n"}, {"instruction": "हमारे दैनिक जीवन में artificial intelligence का उपयोग करने के दो उदाहरण प्रदान करें।\n"}, {"instruction": "एक अंतरिक्षयात्री का काम क्या होता है?\n"}, {"instruction": "व्यायाम शरीर को कैसे प्रभावित करता है?\n"}, {"instruction": "तत्वरित महत्वपूर्ण अवसरों के बारे में शब्दावली का अभ्यास करने के लिए एक शब्द खोज पहेली बनाएँ।\n"}, {"instruction": "सिंडरेल्ला की कहानी का सारांश दीजिए।\n"}, {"instruction": "जीजा पिरामिडों में क्या इतना विशेष है?\n"}, {"instruction": "एक आईके बुकशेल्फ को एकत्रित करने के लिए निर्देश सूची बनाएँ।\n"}, {"instruction": "अपने घर के लिविंग रूम का विवरण दीजिए।\n"}]