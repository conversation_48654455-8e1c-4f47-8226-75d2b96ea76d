[{"instruction": "$45 की खरीदारी के लिए विक्रय कर गणना करें।\n"}, {"instruction": "निम्नलिखित वाक्य में उल्लिखित किसी व्यक्ति, स्थान या वस्तु की पहचान लगाएं। आउटपुट वस्तुओं की एक अैरेंग होनी चाहिए।\nउसने बीच पर एक यात्रा की और वॉलीबॉल ले आई।"}, {"instruction": "बाँस से निर्माण के फायदों के बारे में एक लेख के लिए एक आकर्षक शीर्षक बनाएं।\n"}, {"instruction": "मुफ्त स्वास्थ्य सेवा प्रदान करने के आर्थिक लाभों का विवरण दें।\n"}, {"instruction": "एक सरल कैलकुलेटर के लिए एक क्लास डिज़ाइन करें।\n"}, {"instruction": "निम्नलिखित वाक्य की गुणवत्ता का मूल्यांकन करें:\n\"हमारे अध्ययन की सामग्री को बेहतर समझने के लिए, उदाहरणों के माध्यम से जाना एक अच्छा विकल्प है।\"\n"}, {"instruction": "निम्नलिखित कहानी में तीन चरित्रों की पहचान करें।\nजॉन, एक युवा साहसी, और उसका दोस्त एमी एक खोज में शामिल होते हैं जिसमें एक जादुई वस्तु को ढूंढना होता है जो एक अंधेरे और खतरनाक गुफा में छुपा हुआ है।"}, {"instruction": "आपको एक वाक्य दिया जाता है, जिसे संशोधित करके उसे व्याकरणीय रूप में सही बनाएँ।\nहवा तेजी से और उग्रता से बह रही है।"}, {"instruction": "जर्मनी के इतिहास और संस्कृति के बारे में एक प्रस्तुति बनाएँ।\n"}, {"instruction": "वह ग्राहक सेवा रणनीति का वर्णन करें जिसे एक व्यवसाय अपनाना चाहिए।\n"}, {"instruction": "दिए गए इनपुट वाक्यानुसार, एक ऐसा वाक्य उत्पन्न करें जिसके अंदर एक ही तर्ज और अर्थ हो।\nमुझे वास्तव में इसे करना नहीं है।"}, {"instruction": "घरेलू बजट को 10% तक कम करने के लिए एक योजना बनाएं।\nटोटल घरेलू बजट $2,000 है।"}, {"instruction": "टीम के हिस्से के रूप में काम करने के लाभ क्या हैं?\n"}, {"instruction": "इस वाक्य में दृष्टिकोण पहचानें: \n\nतितलियाँ खुशी से बगीचे में उड़तीं।\n"}, {"instruction": "एक कंप्यूटर प्रोग्राम का विशिष्ट उदाहरण प्रदान करें जो मशीन लर्निंग का उपयोग करता हो।\n"}, {"instruction": "एक फ़्लो चार्ट बनाएं जिससे डॉक्टर बनने का कदम समझाया जा सकता है।\n"}, {"instruction": "एक वितरण सेवा के तीन मुख्य ग्राहकों का नाम बताएँ।\n"}, {"instruction": "बताएं कि अल्बर्ट आइंस्टीन अत्यधिक प्रभावशाली वैज्ञानिकों में से एक के रूप में जाना जाता है।\n"}, {"instruction": "दी गई घटना के बारे में कुछ तथ्य दो।\nक्यूबाई मिसाइल संकट"}, {"instruction": "प्रत्येक दिए गए वाक्य के लिए 'दक्ष' शब्द को संबंधित पर्यायवाचियों से संबंधित करें।\nउन्होंने पाठ्यक्रम पूरा करने के लिए प्रमाणपत्र कमाया।"}, {"instruction": "कुछ वाक्यों में, बताएं कि अच्छे सुनने वाले होना क्यों महत्वपूर्ण है।\n"}, {"instruction": "ऊर्जा संरक्षित करने के तीन तरीके बताएं।\n"}, {"instruction": "एक हार्ड फोर्क और सॉफ्ट फोर्क के बीच क्या अंतर है।\n"}, {"instruction": "एक फिटनेस ऍप में शामिल करने के लिए एक नई सुविधा वर्णित करें।\n"}, {"instruction": "प्लास्टिक का उपयोग कम करने के लिए एक तरीका सुझाएं।\n"}, {"instruction": "बताएं कि एक संगठन डिजिटल मार्केटिंग में निवेश क्यों करना चाहिए।\n"}, {"instruction": "बताएं जब आपको मानसिक ताक़त दिखानी पड़ी थी।\n"}, {"instruction": "दंड विधि और नागरिक विधि के बीच अंतरों की पहचान करें।\n"}, {"instruction": "अपने शब्दों में बताएं कि चेरनोबिल आपदा क्यों हुई।\n"}, {"instruction": "इस प्रकार के उत्पाद के फायदे और नुकसान बताओ।\nजैविक खाद्यान्न"}, {"instruction": "नया कॉमिक स्ट्रिप बनाएं।\n"}, {"instruction": "निम्नलिखित दो चीजों की तुलना के लिए एक उपमा बनाएं।\nएक क्रिकेट और एक हम्मिंगबर्ड की तुलना के लिए एक उपमा।"}, {"instruction": "एक बुज़ुर्ग व्यक्ति के बारे में एक छोटी सी कहानी लिखें जो एक बागवानी शुरू करता है।\n"}, {"instruction": "एक ऐसा 5-शब्दीय वाक्य बनाएं जो सभी शब्दों में आलितेरेशन हो।\n"}, {"instruction": "एक विज्ञान कथा बनाएं।\n"}, {"instruction": "माइक्रोसॉफ्ट के संस्थापक कौन हैं?\n"}, {"instruction": "दिए गए अवधारणा का एक जार्गन मुक्त विवरण लिखें।\nअवधारणा: क्लाउड कंप्यूटिंग"}, {"instruction": "Google Home और Amazon Echo की तुलना करें और उन्हें तुलना करें।\n"}, {"instruction": "दो वस्तुओं की तुलना करें और समझाएं कि एक दूसरे से बेहतर क्यों है।\nवस्तु 1: एप्पल टीवी\nवस्तु 2: गूगल क्रोमकास्ट"}, {"instruction": "कृपया 15 से 18 साल के बीच के युवाओं को आकर्षित करने वाली एक फ़िल्म सुझाएं।\n"}, {"instruction": "सबसे आम शॉपिंग कार्ट छोड़ने का कारण क्या है?\n"}, {"instruction": "1999 में रिलीज़ हुई एक लोकप्रिय फिल्म का नाम बताएं।\n"}, {"instruction": "इस वाक्य को सही व्याकरण के साथ दोबारा लिखें।\nउस फिल्म को मैंने कल रात देखी थी।"}, {"instruction": "एक ईमेल को \"स्पैम\" या \"हैम\" के रूप में वर्गीकृत करें।\nविषय: वेकेशन पैकेज पर 80% छूट पाएँ"}, {"instruction": "खगोलीय डेटा क्लस्टरिंग के लिए एक विधि विकसित करें।\n"}, {"instruction": "टेक्स्ट को संपादित करें ताकि यह एसोसिएटेड प्रेस (एपी) शैली गाइड का पालन करता हो।\nदुकान के प्रबंधक मिस्टर डेविड जोन्स ने आज नई दुकान का उद्घाटन किया।"}, {"instruction": "एक यात्रा ब्लॉग के लिए एक रचनात्मक नाम सोचें।\n"}, {"instruction": "आधुनिक समाज द्वारा सबसे ज्यादा अपनाया गया कौन सा कला का रूप है?\n"}, {"instruction": "वयस्कों के लिए अनुशंसित बीएमआई सीमा क्या है?\n"}, {"instruction": "इंटरनेट के साथ एक संभावित समस्या की पहचान करें, और फिर एक समाधान सुझाएं।\n"}, {"instruction": "इन देशों की क्रमबद्धीकरण करें जो स्पेनिश को प्रथम भाषा के रूप में बोलने वाले लोगों की संख्या के अनुसार हो।\nमेक्सिको, स्पेन, बोलीविया, चिली"}, {"instruction": "नारंगी जूस का स्वाद बताएँ।\n"}, {"instruction": "दिए गए संक्षेप के आधार पर, इस कहानी का नैतिक सीख क्या है?\n"}, {"instruction": "एक ग्राफिक टी-शर्ट डिजाइन करें।\nएक मजेदार पंगत जैसे: \"ठक गए हो\"।"}, {"instruction": "एक प्रोग्राम बनाएं जो दिए गए पाठ की सारांशित सामग्री को 100 से 500 शब्दों तक का पाठ उत्पन्न करता है।\n1884 और 1885 के बीच, जर्मन केमिस्ट आगस्ट केकुले ने बेंजीन रिंग की अवधारणा विकसित की: बेंजीन में छह कार्बन अणु हर दो सेन्टिमेटर और डबल बंधों के बीच एक नियमित हेक्सेगनल व्यवस्था में आपस में जुड़े हुए थे।"}, {"instruction": "आधुनिक कार्यस्थल में तकनीक के उभरते हुए सम्बन्ध पर निबंध के लिए एक विषय-वाक्य बनाएं।\n"}, {"instruction": "ट्रांसफरेबल स्किल्स का एक उदाहरण दें।\n"}, {"instruction": "एक पौधे और एक मानव के बीच तुलना करें।\n"}, {"instruction": "किताब 1984 का विषय क्या है?\n"}, {"instruction": "इंटरनेट के पांच फायदों की सूची बनाएँ।\n"}, {"instruction": "बताएं कि जीरो-सम खेल क्या होता है।\n"}, {"instruction": "दिए गए उत्पाद के लिए एक उत्पाद समीक्षा उत्पन्न करें।\nकोकोप्रो टी-शर्ट"}, {"instruction": "निम्नलिखित नौकरी का वर्णन करने के लिए एक उपयुक्त उपमा बनाएँ:\nसोशल मीडिया प्रबंधक"}, {"instruction": "जो घटनाएं हुई हैं, उन्हें उनकी क्रमबद्धता में बताएं।\nसमुद्र तट पर जाना, रेस्तरां में भोजन करना, रोलरकोस्टर की सवारी।"}, {"instruction": "कृपया सब शांत रहें।\n"}, {"instruction": "Apple Homepod और Google Home के बीच अंतरों की तुलना करें।\n"}, {"instruction": "निम्नलिखित पंक्तियों को दिए गए होने पर, ऐसी एक कविता लिखें जो उन्हें एक सृजनात्मक तरीके से एकीकृत करती हो।\n\"आँखें आत्मा के दरवाजे होती हैं'\n'समय एक भ्रम है'\n'एक सुरक्षित हृदय बुद्धिमान सलाहकार होता है'।\""}, {"instruction": "एक नए व्यंजन बनाएं जो आप स्वयं बचे हुए खाने से बना सकते है।\nबचे हुए खाने में बने हुए दाल, उबली हुई ब्रोकोली और सफेद चावल हैं।"}, {"instruction": "दो तारीखों के बीच बिक्री की कुल राशि की गणना करें।\nशुरू तिथि: 8/1/2020\nसमाप्ति तिथि: 8/30/2020"}, {"instruction": "दिए गए सिस्टम में समस्या का निदान करें।\nकार्यालय में एयर कंडीशनर सही ढंग से काम नहीं कर रहा है।"}, {"instruction": "JavaScript में लिखे गए निम्नलिखित कोड को Python में रूपांतरित करें।\nवेर सम = 0;\nफॉर (वेर आई = 1; आई < 10; आई++) {\n  अगर (आई % 2 === 0) {\n    सम += आई;\n  }\n}"}, {"instruction": "\"अज्ञान सुख है\" वाक्य का विश्लेषण करें।\n"}, {"instruction": "इस वाक्य को ऐसे बदलें कि इसका भाव मजाकिय बन जाए: \"हर कोई यह सुनिश्चित कर लेना चाहिए कि वे अपनी सीट बेल्ट पहन रहे हैं।\"\n"}, {"instruction": "यदि निम्नलिखित वाक्यांश उचित अंग्रेज़ी में है तो पता लगाएँ\nहम खरीदारी के लिए जा रहे हैं।"}, {"instruction": "एक कार्य लिखें जो उत्पाद की मूल मूल्य और छूट प्रतिशत देते हुए उसकी डिस्काउंट कीमत की गणना करता है।\n"}, {"instruction": "एक स्टैंड-अप कॉमेडी मोनोलॉग का स्क्रिप्ट पेअरेंटहुड के विषय पर लिखें।\n"}, {"instruction": "इस वाक्य को पुनर्वचन करें ताकि आज की दुनिया के संदर्भ में अधिक प्रासंगिक बनाया जा सके।\nभूतकाल में लोग अधिकतर यातायात के लिए घोड़ों का उपयोग करते थे।"}, {"instruction": "कृपया एक विज्ञान-कल्पित विषय के साथ अधिकतम 500 शब्दों की एक छोटी कहानी बनाएं।\n"}, {"instruction": "'<PERSON><PERSON><PERSON> and the Three Bears' की कहानी का सारांश बनाएँ।\n"}, {"instruction": "\"Collaborative Filtering\" की परिभाषा खोजें।\n"}, {"instruction": "गृहण करें कि सही उपयोग होमोनिम्स के लिए निम्नलिखित वाक्यांश का विश्लेषण करें।\nमैं उसे दुकान तक चलते हुए देखा।"}, {"instruction": "एक तार्किक तरीके में निम्नलिखित निर्देशों की सूची को फिर से व्यवस्थित करें:\nप्रस्तुति को समाप्त करें, अलार्म सेट करें, ईमेलों का जवाब दें।"}, {"instruction": "उन उदाहरणों में से एक दें जहाँ एआई स्वास्थ्य सेवा में सुधार करने के लिए उपयोग किया जा सकता है।\n"}, {"instruction": "संयुक्त राज्य अमेरिका में वर्तमान आप्रवासन नीतियों का सारांश करें।\n"}, {"instruction": "फ्रेंच में नमस्ते कैसे कहते हैं?\n"}, {"instruction": "\"कभी हार न मानना\" के लिए उदाहरण वाक्य उत्पन्न करें।\n"}, {"instruction": "इस तर्क का मूल्यांकन करें: सरकार को सार्वजनिक स्वास्थ्य व्यवस्था को वित्त पूंजी प्रदान करने में मदद करने के लिए कर बढ़ाना चाहिए।\n"}, {"instruction": "सालाना संयुक्त ब्याज दर 6% वाले एक बैंक खाते में एक प्रारंभिक $100 जमा राशि के साथ पैसे की राशि की गणना करें।\n"}, {"instruction": "एक कुत्ते और एक बिल्ली की कहानी लिखें जो सबसे अच्छे दोस्त बन जाते हैं।\n"}, {"instruction": "कारण बनाएँ कि मल्टीटास्किंग उत्पादकता को बढ़ा सकता है।\n"}, {"instruction": "लॉस एंजेल्स से लास वेगास तक ड्राइव करने में कितना समय लगेगा, अनुमान लगाएं।\n"}, {"instruction": "एक ऐसा सामान्य खाद्य पदार्थ नाम बताएं जो सबसे आसानी से नाश्ते के लिए तैयार किया जा सकता है।\n"}, {"instruction": "एक बजट बनाने के लिए ऑनलाइन संसाधनों की सूची निर्दिष्ट करें।\n"}, {"instruction": "एक सड़क यात्रा पर ले जाने वाली 5 वस्तुओं की सूची बनाएँ।\n"}, {"instruction": "एक नगर पालिका सदस्य के जीवन का एक दिन वर्णन करें।\n"}, {"instruction": "एक गैर-शराबी पेयों की सूची तैयार करें।\n"}, {"instruction": "दिए गए विषय पर तीन प्रश्न बनाएं जो एक मूल्यांकन उपकरण के रूप में उपयोग किए जा सकते हैं।\nविषय है मानव-कंप्यूटर इंटरेक्शन (एचसीआई)।\n\n(Translated text: The topic is Human-Computer Interaction (HCI).)"}, {"instruction": "अपने स्थानीय पार्क के वातावरण को सुधारने का एक योजना प्रदान करें।\n"}, {"instruction": "बताएं कि आप कैसर Cipher एल्गोरिथ्म को कैसे लागू करेंगे।\n"}, {"instruction": "सोशल मीडिया की शक्ति का उपयोग करने वाले एक प्रभावी मार्केटिंग अभियान का वर्णन करें।\n"}, {"instruction": "एक विधि बनाएं मोटिवेटेड रहने के लिए।\n"}, {"instruction": "बताएं कि क्वांटम कंप्यूटर क्या होता है।\n"}, {"instruction": "इस मल्टीपल च्वाइस प्रश्न का उत्तर दें।\n\nQ. \"To Kill a Mockingbird\" किताब कौन लिखा?\nA. मार्क ट्वैन\nB. हार्पर ली\nC. चार्ल्स डिकेंस\nD. अर्नेस्ट हेमिंगवे\n"}, {"instruction": "जिराफ के बारे में एक रोचक तथ्य बताएं।\n"}, {"instruction": "आपको 'हमारे पेड़ बचाओ' नामक नए अभियान के लिए एक प्रतिनिधित्व लोगो विकसित करने की आवश्यकता है।\n"}, {"instruction": "किसी निश्चित कंपनी के लिए स्टॉक मूल्य का अनुमान लगाएं।\n"}, {"instruction": "किसी प्रेस रिलीज़ को लिखने के लिए चरणों की सूची बनाएं।\n"}, {"instruction": "एक शाकाहारी भोजन लेने के लिए 5-चरणी योजना बनाएं।\n"}, {"instruction": "निम्नलिखित पाठ का प्रकार निर्धारित करें। कथात्मक के लिए 1 और गैर-कथात्मक के लिए 0 आउटपुट करें।\nफिल्म एक परिवार की यात्रा का पालन करती है जब वे एक मुश्किल फैसले का सामना करते हैं।\n\nOutput: फिल्म एक परिवार की यात्रा का पालन करती है जब वे एक मुश्किल फैसले का सामना करते हैं।\n\nTranslated text: फिल्म एक परिवार की यात्रा का पालन करती है जब वे एक मुश्किल फैसले का सामना करते हैं।"}, {"instruction": "एक डॉक्टर की दैनिक रूटीन का वर्णन करें।\n"}, {"instruction": "\"Love\" के लिए 10 समानार्थक शब्द बनाएँ।\n"}, {"instruction": "एक आउटडोर कैफे का वर्णनात्मक अनुच्छेद बनाएं।\n"}, {"instruction": "\"मेरे फोन की बैटरी अचानक मर गयी।\" के लिए एक उपयुक्त प्रतिक्रिया उत्पन्न करें।\n"}, {"instruction": "एक किताबों की सूची को कल्पना और गैर-कल्पना में श्रेणीबद्ध करें।\n, राजा किलर, स्वस्थ शरीर, बारामूला के लोग।"}, {"instruction": "भारत में उपयोग में आने वाली मुद्रा का पहचान करें।\n"}, {"instruction": "दो शहरों की जलवायु की तुलना करें।\nतोक्यो और न्यूयॉर्क"}, {"instruction": "इस्तेमाल करते हुए एक वाक्य लिखें जो निम्नलिखित शब्दों का उपयोग करता हो: कंक्रीट और नींव।\n"}, {"instruction": "इस करियर के फायदे और नुकसान की सूची बनाएँ।\nकरियर: खेल कोच"}, {"instruction": "एक वाक्य को फिगुरेटिव भाषा का अधिक जटिल इस्तेमाल करने के लिए अनुलेखन करें।\nवह एक तेंदुआ की तरह दौड़ता है।"}, {"instruction": "प्राचीन यूनानियों को सूर्यग्रहण के कारण क्या लगता था?\n"}, {"instruction": "व्याख्या करें, सीधी और अप्रत्यक्ष जटिलता के बीच का अंतर।\n"}, {"instruction": "'बहुवचन फॉरम' ने कौन गीत 'स्ट्रॉबेरी फील्ड्स फॉरएवर' रचा था?\n"}, {"instruction": "दिए गए ध्वनि तरंग का विश्लेषण करें।\nसमुद्र की ध्वनि तरंग।"}, {"instruction": "इस वाक्य को यूनिक ढंग से दोहराएं जो वही अर्थ दर्शाता हो लेकिन उसी शब्दों को शामिल न करता हो।\nमैं तापमान सहन नहीं कर सकता हूँ।"}, {"instruction": "\"Rome ka dobara koi ane nahi\" ko sankshipt karne ke liye aapas mein likhein.\n"}, {"instruction": "शब्द \"कुत्ता\" के लिए एक शब्द संबंध उत्पन्न करें।\n"}, {"instruction": "नौकरी का विवरण लिखें।\nमार्केटिंग प्रबंधक"}, {"instruction": "एक दिए गए स्थिति के लिए उचित कार्रवाई निर्धारित करें।\nएक उपयोगकर्ता अपना पासवर्ड भूल गया है।"}, {"instruction": "हर महीने $5000 के लिए एक परिवार बजट बनाएँ।\n"}, {"instruction": "\"बदलाव\" शब्द का उपयोग करके राजनीतिक अभियान के लिए एक नारा बनाएं।\n"}, {"instruction": "एक पालतू पशु सम्पादन व्यवसाय के लिए एक मार्केटिंग अभियान नारा बनाएं।\n"}, {"instruction": "निम्नलिखित दावे का मूल्यांकन करें: \"समुद्र एक जीवनु उपकरण है\"\n"}, {"instruction": "निम्नलिखित कोड सेगमेंट के लिए सुधार सुझाएं जो इसे और पायथानिक बनाने में मदद करें।\narray = [1, 2, 3, 4]\nfor x in range(len(array)):\n  array[x] += 5"}, {"instruction": "C++ लाइब्रेरी लिखें जो लिंक्ड लिस्ट को अमल में लाती हो।\n"}, {"instruction": "हाल के समय में बाढ़ने वाले प्रजनक आबादी के लिए एक संभव व्याख्या दें।\n"}, {"instruction": "\"भौंक\" शब्द का उपयोग करके राइम बनाएं।\n"}, {"instruction": "इस संदेश के लिए सबसे अच्छा एन्क्रिप्शन का प्रस्ताव दें।\nयह एक गोपनीय खरीदारी के बारे में संवेदनशील संदेश है।"}, {"instruction": "एक तलीदार पेशेवर के लिए एक रिज्यूम कैसा होना चाहिए का उदाहरण बनाएँ।\n"}, {"instruction": "हम ईमेलों के एंगेजमेंट दर को कैसे बढ़ा सकते हैं?\n"}, {"instruction": "वाक्य \"दिन गिनना बंद करो, दिनों को महत्वपूर्ण बनाओ\" का अनुवाद करें।\n"}, {"instruction": "दिए गए वाक्य को संपादित करें और इसे तकनीकी रूप से अधिक सटीक बनाएं।\nइलेक्ट्रॉन्स और प्रोटॉन में उल्टे आकर्षण के वे विभिन्न आवेश लेते हैं।"}, {"instruction": "एक ब्रांड नाम उत्पन्न करें।\n"}, {"instruction": "आपके जीवन में सबसे महत्वपूर्ण सबक क्या है?\n"}, {"instruction": "क्या आपको कोई चीज़ करने की अनुमति है अगर आप नहीं जानते कि यह कानूनी है या नहीं?\n"}, {"instruction": "कृपया एक वेब एप्लिकेशन के बारे में विवरण प्रदान करें, जिसमें इस्तेमाल की जाने वाली प्रोग्रामिंग भाषा, एप्लिकेशन का उद्देश्य, मुख्य विशेषताओं का विवरण और डिप्लॉयमेंट की प्रक्रिया शामिल हो।\n"}, {"instruction": "Python में एक कोड जेनरेट करें जो किसी भी दिए गए संख्या के वर्गमूल की गणना करता है।\n"}, {"instruction": "वाक्य में सही स्वामित्व-बोधक सर्वनाम चुनें।\nपैट्रीशिया और मैं इटली जाने के बारे में बात कर रहे थे। उसकी आगामी।"}, {"instruction": "एक वर्गीकरण मॉडल बनाएं जिससे पता लगाया जा सके कि दिए गए स्तनधारी जानवर शाकाहारी हैं या मांसाहारी हैं।\nफेनेक फॉक्स"}, {"instruction": "निम्नलिखित कविता का विश्लेषण करें।\nदेवदारू\nफिलिप लार्किन द्वारा\n\nदेवदारू पेड़ पत्ते लगा रहे हैं\nजैसे कुछ कहना है;\nआखिरी फूलों का बढ़ना है \nउनकी हरीयाली एक प्रकार का दुख है।"}, {"instruction": "अपने उत्पाद की साप्ताहिक बिक्री दिखाने वाली एक दृश्यीकरण बनाएं।\n"}, {"instruction": "HTML में एक फ़ॉर्म बनाएं जो उपयोगकर्ताओं को बहुविकल्प प्रश्नों का उत्तर देने की अनुमति देता है।\nHTML में एक फ़ॉर्म बनाएं जो उपयोगकर्ताओं को बहुविकल्प प्रश्नों का उत्तर देने की अनुमति देता है।\n\nप्रश्न:\nQ1. निम्नलिखित में से कौन से जानवर मामलियों में से हैं?\na. मानव\nb. मछली\nc. तोता\nd. सांप\n\nQ2. फ्रांस के राष्ट्रपति कौन है?\na. एंगेला मेर्केल\nb. रेकेप ताययिप एर्दोगान\nc. जो बाइडेन\nd. इमेनुएल माकरों"}, {"instruction": "निम्नलिखित श्रेणियों का उपयोग करके 10 स्टॉक का एक पोर्टफोलियो बनाएं।\nउद्योग: टेक्नोलॉजी, जोखिम: उच्च, भौगोलिक स्थान: वैश्विक"}, {"instruction": "एक कहानी के प्रोटैगोनिस्ट को एक यात्रा पर लेने वाली सुरक्षा उपायों की सूची बनाएं।\n"}, {"instruction": "एआई शिक्षा पर कैसा प्रभाव डाल सकती है?\n"}, {"instruction": "कम कार्बोहाइड्रेट वाले सब्जियों, फलों और प्रोटीन की सूची बनाएं।\n"}, {"instruction": "यूरोपीय संघ के नॉर्थवेस्ट इलाके में कौन से देश हैं?\n"}, {"instruction": "एक उत्पाद के लिए आवश्यक घटकों की व्यापक सूची बनाएं।\n"}, {"instruction": "एक उत्पाद के लिए एक बिक्री भाषण प्रदान करें।\nस्मार्टफोन चार्जिंग स्टेशन"}, {"instruction": "बताएं कि हैंडव्राइटिंग को पहचानने के लिए मशीन लर्निंग मॉडल का उपयोग कैसे किया जा सकता है।\n"}, {"instruction": "चलचित्र स्टार वॉर्स: द एम्पायर स्ट्राइक्स बैक का सारांश बनाएँ।\n"}, {"instruction": "समानता के मूल सिद्धांतों की व्याख्या करें।\n"}, {"instruction": "कुत्तों के बारे में एक रोचक तथ्य बताओ।\n"}, {"instruction": "यह निर्धारित करें कि दिए गए ट्वीट की भावना सकारात्मक, नकारात्मक या न्यूट्रल है।\nमुझे गिटार बजाने और महान आउटडोर्स का अन्वेषण करना पसंद है!"}, {"instruction": "बारिश क्यों होती है?\n"}, {"instruction": "एक पालतू जानवर के स्वामित्व के फायदे और नुकसानों के बारे में एक ब्लॉग पोस्ट लिखें।\n"}, {"instruction": "टेक्नोलॉजी पर एक लेक्चर के विषयों की सूची तैयार करें।\n"}, {"instruction": "दो पात्रों के बीच एक संवाद बनाएं जो उनकी भावनाओं को प्रदर्शित करता है कि वह स्थिति के बारे में कैसा महसूस कर रहे हैं।\n"}, {"instruction": "एक यात्रा स्थलों की सूची बनाएं जो आप देखना चाहते हैं।\n"}, {"instruction": "दक्षिण कोरिया की संस्कृति का अनुभव करने के बारे में एक यात्रा ब्लॉग पोस्ट लिखें।\n"}, {"instruction": "एक वाक्य दिया गया है, बताइए कि आप क्यों सोचते हैं कि यह सही है।\nएक सफल रिश्ते में अच्छा संचार आवश्यक है।"}, {"instruction": "जुलाई 28वीं, 2020 को यूनिक्स टाइमस्टैंप में रूपांतरित करें।\n"}, {"instruction": "\"बादल कंप्यूटर के लिए होता है जैसे कि बुकशेल्फ बुक्स के लिए होता है।\"\n"}, {"instruction": "एक पर्सेप्ट्रॉन की मुख्य विशेषताओं का रूपरेखा बनाओ।\n"}, {"instruction": "एक एल्गोरिथम का उदाहरण तैयार करें जो सबसे छोटे से सबसे बड़े संख्याओं की एक सूची को क्रमबद्ध करने के लिए हो।\n"}, {"instruction": "एक सामान्य मॉक साक्षात्कार प्रश्नों का सेट बनाएं।\n"}, {"instruction": "जब लेखक ने \"उसके कार्य शब्दों से ज्यादा सुनाई देते थे\" लिखा था, तब उन्होंने क्या मतलब था?\n"}, {"instruction": "एक दिए गए स्ट्रिंग में सभी शब्दों को उलट देने के लिए एक जावास्क्रिप्ट प्रोग्राम लिखें।\n"}, {"instruction": "कर्मचारियों को अपनी वेतन-पत्रिका देखने के लिए एक ग्राफिकल उपयोगकर्ता इंटरफेस डिजाइन करें।\n"}, {"instruction": "निम्नलिखित वाक्यांश अंग्रेजी में एक मान्य वाक्य है या नहीं, इसे निर्धारित करें।\nबिल्ली कमरे के तीव्रता से तेज़ी से दौड़ती हुई गई।"}, {"instruction": "एक जांच उपकथ के एक उदाहरण की पहचान करें।\n"}, {"instruction": "हाइड्रोजन पेरोक्साइड का मुख्य घटक क्या है?\n"}, {"instruction": "एक बचाव पैक की आइटमों की सूची तैयार करें।\n"}, {"instruction": "उस व्यक्ति को जो इस विषय से अनभिज्ञ है, के साथ \"कृत्रिम बुद्धिमत्ता\" की अवधारणा का वर्णन करें।\n"}, {"instruction": "एक पर्वत शिविर यात्रा के लिए एक सूची बनाएं।\n"}, {"instruction": "फुटबॉल खेल के नियम समझाइए।\n"}, {"instruction": "3/5 को लगभग अंक में परिमाणित करें।\n"}, {"instruction": "तीन पंक्तियों का एक हाइकू कविता लिखें, प्रत्येक पंक्ति में पाँच, सात और पाँच स्वरों की।\n"}, {"instruction": "शब्दकोश में एक ऐसा शब्द खोजें जो सही तरीके से वर्तनी किया गया हो और दिए गए शब्द के समान अर्थ हो।\nशब्दकोश में एक ऐसा शब्द खोजें जो सही तरीके से वर्तनी किया गया हो और दिए गए शब्द के समान अर्थ हो।\ncaire"}, {"instruction": "एक प्रेरणादायक भाषण तैयार करें जिसमें बताया गया हो कि अंतरिक्ष अन्वेषण ज्यादा हानि पहुंचाता है।\n"}, {"instruction": "दिए गए कविता में मौजूद विषयों का वर्णन करें।\nहम रात में निकलते हैं\nजहां सिर्फ तनहाई की तरह तारे रोते हैं\nऔर अंधेरे में प्यार करते हैं।"}, {"instruction": "मुझे मारीजुआना सिगरेट पीने के तीन जाने गए छोटी अवधि के प्रभाव बताएं।\n"}, {"instruction": "एक लोकप्रिय शौक का पता लगाएं जिसमें किशोरों को लगाव होता है।\n"}, {"instruction": "वेबसाइट पर उपयोगकर्ता अनुभव सुधारने के लिए क्रियान्वित आइटम की सूची तैयार करें।\n"}, {"instruction": "क्रम 3 का एक त्रिकोणीय मैट्रिक्स में कितने तत्व होते हैं?\n"}, {"instruction": "अगले 5 साल में प्रौद्योगिकी में कैसे बदलाव होंगे, उन्हें पूर्वानुमान लगाएं।\n"}, {"instruction": "इस वाक्य में बोल्ड किए गए शब्दों के भाषा भाग को बदलें।\nहमने उनकी सुझावों को ध्यान से ध्यान दिया।"}, {"instruction": "3! की मान ढूंढें।\n"}, {"instruction": "“इस रेस्टोरेंट ने एक महान अनुभव दिया था!” को भावना विश्लेषण कार्य में वर्गीकृत करें।\n"}, {"instruction": "प्राकृतिक चयन और कृत्रिम चयन की तुलना करें।\n"}, {"instruction": "एक चित्र की 4 विवरण उत्पन्न करें जिसमें पहाड़, नीला आकाश और एक पेड़ प्रतिबंध में हों।\n"}, {"instruction": "लोग सोशल मीडिया का इस्तेमाल करते समय सुरक्षित कैसे रह सकते हैं?\nइस्तेमाल करते समय सुरक्षित कैसे रह सकते हैं?"}, {"instruction": "एक व्यक्ति की आंखों के रंग का वर्णन करने के लिए एक उपमा उत्पन्न करें।\n"}, {"instruction": "मशीन लर्निंग में क्लस्टरिंग का उद्देश्य क्या है?\n"}, {"instruction": "स्वतंत्र गाड़ियों की वर्तमान स्थिति का अध्ययन करें और उनके संभावनाओं का एक संक्षिप्त अवलोकन प्रदान करें।\n"}, {"instruction": "कोई काल्पनिक उपन्यास के लिए कुछ विचारों को ब्रेनस्टॉर्म करें।\n"}, {"instruction": "इन दो शब्दों के बीच संबंध का विवरण वर्तमान में उत्पन्न करें।\nपंखुड़ी, फूल"}, {"instruction": "यदि नकारात्मक और सकारात्मक बयानों को दिया गया हो, तो प्रत्येक बयान को एक भावना स्कोर असाइन करें।\nनकारात्मक बयान: मेरा दिन बहुत खराब था।\nसकारात्मक बयान: मैंने अपने परियोजना पर बहुत अच्छी प्रगति की।"}, {"instruction": "टॉपिक और शोध प्रश्न देकर, एक अनुमान बनाएँ।\nविषय: सीखना\nशोध प्रश्न: हम छात्र सीखने को कैसे बेहतर बना सकते हैं?"}, {"instruction": "एक कार्बोहाइड्रेट का संरचना का विवरण दें।\n"}, {"instruction": "नीचे दिए गए इनपुट का उपयोग करके, संदिग्ध ऑनलाइन गतिविधियों का पता लगाने के लिए एक एल्गोरिथ्म विकसित करें।\nऑनलाइन मंच अक्सर धोखाधड़ी, घोटाले और अन्य गलत व्यवहार जैसी दुराचारी गतिविधियों से निपटने के लिए जूझते हैं।"}, {"instruction": "शिक्षा प्रणाली को सुधारने के दो संभव समाधान सुझाएं।\n"}, {"instruction": "एक वाक्य दिया गया है जिसमें एक अभावित शब्द है, सबसे अच्छा शब्द प्रदान करें।\nवह परिणाम पर________ थी।"}, {"instruction": "वाक्य में शब्द \"लाभदायक\" को अधिक वर्णनात्मक बनाने के लिए इसे बदलें।\nये पौधे लाभकारी प्रभाव डालते हैं।"}, {"instruction": "शताब्दी युद्ध के लिए एक टाइमलाइन बनाएं।\n"}, {"instruction": "अमेरिकी नागरिक युद्ध के प्रमुख कारण क्या थे?\n"}, {"instruction": "कंपनियों के लिए सोशल मीडिया प्रतिष्ठान बनाए रखना क्यों महत्वपूर्ण है, इसका विवरण दीजिए।\n"}, {"instruction": "दिखाओं कि यदि AB एक समकोण होता है, तो ∠A + ∠B = 180°।\n"}, {"instruction": "कोरोनावायरस कैसे मानवों के श्वसन तंत्र को प्रभावित करता है, इसे समझाएँ।\n"}, {"instruction": "बताएं एक ऐसी अनुभव के बारे में जिसमें आपको उस व्यक्ति के साथ काम करना पड़ा जिससे आपको अनुचित या अनुपयुक्त था।\n"}, {"instruction": "वाक्य में वर्णों की संख्या गिनें, समावेश करके खाली स्थान और विराम चिन्ह।\nयह एक स्ट्रिंग है।"}, {"instruction": "बताएं कि जानवर कैसे अपने पर्यावरण के अनुरूप अनुकूलता प्राप्त कर सकते हैं।\n"}, {"instruction": "बताएं कि आप स्कूल से पहले सुबह क्या करते हैं।\n"}, {"instruction": "निम्नलिखित वाक्य एक दौड़ वाक्य है या नहीं, इसकी पहचान करें।\nगाड़ी पुरानी और जंगली थी, लेकिन फिर भी अच्छी तरह से चलती थी।"}, {"instruction": "एक हिंदू देवी का नाम बताओ।\n"}, {"instruction": "एक योजना प्रस्तावित करें जो जंगली ईंधन के उपभोग को कम कर सकती हो।\n"}, {"instruction": "हाइपरटेक्स्ट मार्कअप भाषा (HTML) के उद्देश्य का विवरण दें।\n"}, {"instruction": "जानवर जगत से संबंधित दो जीव खोजें।\n"}, {"instruction": "विज्ञान-फंटासी थीम वाले 2 कहानी विचार बनाएँ।\n"}, {"instruction": "इसे या तो अख्यायिका, रहस्य, डर, या हास्य के रूप में वर्गीकृत करें।\nएक परिवार को अपने छतरी में रहने वाली एक प्राणी की खोज होती है और उसे जंगल में वापस ले जाने में मदद करनी होती है।"}, {"instruction": "एकल छेदन प्रयोग में एक फोटॉन की पथ को ट्रेस करें।\n"}, {"instruction": "कौन सा अमेरिकी राज्य औसत ऊंचाई में सबसे ऊपर है?\n"}, {"instruction": "एक नई तरह की यात्रा सेवा के लिए एक वेबसाइट बनाएं।\n"}, {"instruction": "कुत्ते और बिल्लियों को पालतू जानवर के रूप में तुलना करें।\n"}, {"instruction": "एक व्यक्ति का विविध विशेषण-संज्ञा संयोजन उत्पन्न करें।\n"}, {"instruction": "बताएं कि क्यों अमेरिका के पास दो-पक्षीय व्यवस्था है।\n"}, {"instruction": "इन शब्दों को उनकी वर्तनी के आधार पर दो समूहों में वर्गीकृत करें।\nचमगादड़, पंछी, बल्ब, ब्रश"}, {"instruction": "प्रश्न के लिए सर्वश्रेष्ठ प्रतिक्रिया चुनें।\nजो नवीनीकरणीय ऊर्जा की सबसे अर्थव्यवस्थित फॉर्म है, वह कौन सी है?\n\nA) भूजलगैर-ऊर्जा\nB) सौर\nC) पवन\nD) जलविद्युत्"}, {"instruction": "ऑनलाइन खरीदारी और स्टोर में खरीदारी का तुलनात्मक विवरण दें।\n"}, {"instruction": "ऑनलाइन और ऑफलाइन सीखने के दो तरीकों की तुलना करें।\nऑनलाइन और ऑफलाइन सीखने के दो तरीकों की तुलना करें।"}, {"instruction": "वह स्थितियाँ वर्णित करें जिसके तहत कोई व्यक्ति जमानत पर छूट सकता है।\n"}, {"instruction": "तकनीक का उपयोग समय बचाने के नए तरीकों के लिए विचार करें।\n"}, {"instruction": "संसारी खाद्य पदार्थों के विकल्प सुझाएँ।\n"}, {"instruction": "अगर आपके पास असीमित संसाधनों का उपयोग होता तो आप क्या करेंगे?\n"}, {"instruction": "तीन कंपनियों के नाम बताओ जो इलेक्ट्रिक कार उत्पादित करती हैं।\n"}, {"instruction": "समय पर उपस्थित होने के महत्व को मूल्यांकित करें।\n"}, {"instruction": "एक ऐसा एल्गोरिथ्म डिजाइन करें जो पहले n प्राइम नंबर्स को प्रिंट करता है।\n"}, {"instruction": "कुछ ऐसा बताएं जो एक वकील करना चाहिए।\n"}, {"instruction": "दिए गए वाक्य का विश्लेषण करें और यह नियमित वाक्य है या नहीं, इसका निर्धारण करें।\nउसे अपने लाइसेंस को नवीनीकृत करना था।"}, {"instruction": "दिए गए इनपुट से विषय के बारे में कुछ तथ्य बनाएं।\nएकमत्र नक्षत्र क्या होता है?"}, {"instruction": "निम्नलिखित शब्दों का उपयोग करके एक शुरुआत, एक मध्य और एक समाप्ति वाली कहानी बनाएं:\nअसली टेक्स्ट: \n\nयूनिकॉर्न, कैसल, जंगल \n\nशुरूआत हुई राजमहल में। यूनिकॉर्न के जादुई पांवों के साथ कैसल में घूमते हुए उन्हें एक सुंदर सम्राटा का सामना हुआ। उसने उन्हें बताया कि जंगल में रहने वाले राजकुमार खो गए हैं। यूनिकॉर्न ने उससे सहायता की और उन राजकुमारों को ढूंढ़ने के लिए जंगल में जाने का निर्णय लिया। जंगल खतरनाक था, लेकिन यूनिकॉर्न उन्हें संरक्षित रखने में सफल रहा। आखिरकार, वह राजकुमारों को ढूंढ़ लिया और उन्हें वापस राजमहल में लौटा दिया।"}, {"instruction": "उत्पाद विवरण उत्पन्न करें।\nयह उत्पाद एक वैक्यूम क्लीनर है।"}, {"instruction": "सबसे महत्वपूर्ण प्राकृतिक संसाधनों की पहचान करें।\n"}, {"instruction": "एक प्रतिष्ठान बनाएं जो कोई व्यक्तिगत सहायक से पूछ सकता है।\n"}, {"instruction": "GPT-3 का उपयोग करके स्वचालित ग्राहक सेवा के प्रभाव को ग्राहक संतोष पर समझाएं।\n"}, {"instruction": "एक कंपनी के लिए एक ऐसा 8 शब्दों का नारा बनाएं जो ऑर्गेनिक शैम्पू बेचती है।\n"}, {"instruction": "कमरे को साफ करने के चरणों का सारांश ट्वीट की रचना करें।\n"}, {"instruction": "इस पुस्तक को एक शैली वर्गीकरण दें:\nहैरी पॉटर और फिलॉसफर्स स्टोन"}, {"instruction": "दिए गए सूची से विषम संख्याओं को प्राप्त करने के लिए एक फ़ंक्शन लिखें।\nlist = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\nविभाज्य संख्या प्राप्त करने के लिए एक फ़ंक्शन लिखें।"}, {"instruction": "निम्न डेटा बिंदुओं के आधार पर, पहचानें कि कौन सा प्रकार का मशीन लर्निंग एल्गोरिथ्म उपयोग किया जा रहा है।\nडेटा में एक सौ विभिन्न एयर कंडीशनर से तापमान की रेडिंग शामिल है जो आठ महीनों के अवधि में ली गई है।"}, {"instruction": "कृत्रिम बुद्धिमत्ता के परिणामों को वर्णित करें।\n"}, {"instruction": "एक फ़ंक्शन लिखें जो यह निर्धारित करता है कि क्या दिया गया नंबर एक पूर्ण वर्ग है या नहीं।\nफ़ंक्शन लिखें जो निर्धारित करता है कि क्या दिया गया नंबर एक पूर्ण वर्ग है या नहीं।"}, {"instruction": "इस निर्देश के जवाब में एक GPT सहायक किस प्रकार के आउटपुट को उत्पन्न करेगा?\nएक उत्पाद लॉन्च चक्र की चरणों का वर्णन करें।"}, {"instruction": "चार मस्कटियर्स के नाम बताओ।\n"}, {"instruction": "एक वाक्य से सबसे अधिक उपयोग किए जाने वाले शब्द के दूसरे अक्षर को आउटपुट करें।\nहम सभी अंतर कर सकते हैं। फ़र्क़ पैदा कर सकते हैं।"}, {"instruction": "एक नए कॉटन शर्ट के रंग और बनावट का वर्णन करें।\n"}, {"instruction": "एक विशिष्ट उत्पाद का उपयोग करने के तीन रचनात्मक तरीके बनाएं।\nउत्पाद: सेब\n\nनोट: हिंदी अनुवाद में शीर्षक नहीं होना चाहिए।"}, {"instruction": "डेटाबेस में स्थिति 'भेज दी गई' वाले सभी आइटमों को पुनः प्राप्त करने के लिए एक क्वेरी बनाएं।\n"}, {"instruction": "दिए गए टेक्स्ट को संशोधित करके इसकी व्याकरण गलतियों को ठीक करें।\nयह कुछ अन्य परियोजना है।"}, {"instruction": "किसी प्रसिद्ध लेखक के बारे में बताओ।\n"}, {"instruction": "बताइए कि वैज्ञानिक सिद्धांतों के विकास का समाज पर कैसा प्रभाव पड़ा है।\nबताइए कि वैज्ञानिक सिद्धांतों के विकास का समाज पर कैसा प्रभाव पड़ा है।"}, {"instruction": "कम से कम 500 शब्दों की एक छोटी सी कहानी लिखें जिसमें दिए गए चरित्र शामिल हों।\nचरित्र: मार्क, सारा और एलिस।"}, {"instruction": "एक शब्द 'एसिटिलकोलीन' दिया गया है, इसके मोलेक्यूल के प्रकार का नाम बताएँ।\n"}, {"instruction": "“जवाब दें : 'जर्मनी का सबसे बड़ा पोत कौन सा है?\"\n"}, {"instruction": "\"ट्रांसपेरेंसी\", \"स्ट्रक्चर\", \"इनोवेशन\" शब्दों का उपयोग एक वाक्य बनाने के लिए करें।\n"}, {"instruction": "दुनिया में उच्चतम बेरोजगारी दर वाले पांच देशों की सूची बनाओ।\n"}]