[{"instruction": "निम्नलिखित उत्पाद का विज्ञापन करने के लिए एक चुंबकीय शीर्षक बनाएं।\nतुरंत कॉफ़ी"}, {"instruction": "एक व्यक्ति की प्रतिक्रिया समय का मापने के लिए एक प्रयोग डिजाइन करें।\n"}, {"instruction": "कृत्रिम बुद्धिमत्ता का उपयोग करने के मुख्य लाभों की सूची बनाएं।\n"}, {"instruction": "एक नई HTML पेज बनाएं।\n"}, {"instruction": "\"The Great Gatsby\" का संक्षिप्त विवरण प्रदान करें।\n"}, {"instruction": "मैनेजर्स के लिए महत्वपूर्ण दो पेशेवर विकास कौशल बताएं।\n"}, {"instruction": "एक किले की रक्षा रणनीति डिजाइन करें।\n"}, {"instruction": "क्या संख्या 12 प्राइम है?\n"}, {"instruction": "अधिकतम विश्वास के साथ, अपने सपनों को पूरा करने की क्षमता में विवेक ही सफलता का एक अहम अंग है।\nयह कार तेज है।"}, {"instruction": "समुद्र तल पर उबलते पानी का तापमान खोजें।\n"}, {"instruction": "सूरज की भूमिका फोटोसिन्थेसिस में समझाएं।\n"}, {"instruction": "वेब प्रदर्शन को सुधारने के तीन तरीके बताएं।\n"}, {"instruction": "एक कंपनी अपने उत्पाद डिज़ाइन में ग्राहक सुझाव का उपयोग कैसे कर सकती है?\n"}, {"instruction": "दिए गए वेक्टरों के बीच कोण की गणना करें।\nवेक्टर ए: 4i + 2j\nवेक्टर बी: -3i + 5j"}, {"instruction": "दिए गए लेखक से प्रसिद्ध उद्धरणों की पहचान करें।\nलेखक: अल्बर्ट आइंस्टीन"}, {"instruction": "एक वस्तुओं की सूची दी गई है, जिसमें सूची पर सभी वस्तुओं को शामिल करने वाली कहानी का रूप तय करें।\nवस्तुओं की सूची: अंतरिक्ष जहाज, एलियन, पार्क, नदी, हीरा"}, {"instruction": "'संरचना और कार्य के उपलब्धिता के सिद्धांत' की परिभाषा क्या है?\n"}, {"instruction": "बीच पर प्राकृतिक वस्तुओं का उपयोग करके एक खेल डिजाइन करें।\n"}, {"instruction": "एक दिए गए घटना के संभव कारण का वर्णन करें।\nकिशोरों में मधुमेह में वृद्धि।"}, {"instruction": "एक आदर्श वेबसाइट की देखभाल और भाव का वर्णन करें।\n"}, {"instruction": "अपने दोस्तों के साथ उनिक क्रियाएँ बताएं जो आप घर से निकले बिना कर सकते हैं।\n"}, {"instruction": "एक नए टीम में शामिल होने के लिए एक युवा एथलीट की फैसले के बारे में कुछ सुनाओ।\n"}, {"instruction": "डेटा द्वारा चलाए जाने वाले एक एल्गोरिथ्म का उदाहरण दीजिए।\n"}, {"instruction": "कोविड-19 राहत के लिए एक फंडरेजिंग इवेंट के लिए एक प्रस्ताव लिखें।\n"}, {"instruction": "तीन पूर्णांकों का औसत निकालने के लिए एक कोड लिखें।\n"}, {"instruction": "निम्नलिखित स्थिति के लिए एक प्रश्न सुझाएं।\nवह कंपनी की एक नई कर्मचारी है और उसे एक सप्ताह के भीतर व्यापारिक यात्रा पर जाना होगा।"}, {"instruction": "ज्यादातर लोग कब सेवानिवृत्त होते हैं?\n"}, {"instruction": "इस लेख का सारांश:\n\nhttps://www.nationalgeographic.com/travel/destinations/north-america/united-states/tennessee/great-smoky-mountains-national-park/\n"}, {"instruction": "आप निम्नलिखित वाक्य को कैसे संरचित करेंगे ताकि यह अधिक स्पष्ट रूप से लिखा जाए और आसानी से समझा जा सके?\nविश्व में देशों के बीच बढ़ते व्यापार से गरीबी कम होने में मदद मिलती है, लेकिन कुछ अर्थशास्त्री चिंतित हैं कि यह अर्थव्यवस्थाओं को नुक़सान पहुँचा सकता है।"}, {"instruction": "व्यवसाय के विषय से संबंधित एक वाक्य उत्पन्न करें।\n"}, {"instruction": "दिए गए डेटा के लिए माध्य और मानक विचलन की गणना करें।\n8, 4, 7, 7, 5, 2, 10"}, {"instruction": "डेटा माइनिंग की अवधारणा को समझाने वाला एक उपमा बनाएं।\n"}, {"instruction": "एक नैतिक दुविधा का वर्णन करें और सबसे अच्छा समाधान चुनने के लिए कैसे समझाएं।\n"}, {"instruction": "पैसे बचाने के लिए रणनीतियों का सारांश बनाएं।\nलंबे समय के लक्ष्य निर्धारित करके, अपनी खर्चों को ट्रैक करके, बजट बनाकर, भावनात्मक खरीदारी से बचकर और उच्च रिटर्न मौकों में निवेश करके पैसे बचाएं।"}, {"instruction": "दिए गए तत्वों को जोड़कर एक कहानी उत्तेजक बनाएँ।\nएक जादुई औषधि, एक रहस्यमय पुस्तकालय, एक चुराया गया वस्तु।"}, {"instruction": "एक अस्पष्ट कलाकृति के एक ड्राइंग उत्पन्न करें।\n"}, {"instruction": "एक स्ट्रिंग दी गई हो तो, उसे पैलिंड्रोम बनाने के लिए आवश्यक न्यूनतम हटाने की संख्या ढूंढें।\nGTACAT"}, {"instruction": "नर्सरी दीवार के लिए एक ऐसा चित्र सुझाएं जो नीले फर्नीचर को पूर्ति करेगा।\n"}, {"instruction": "मशीन लर्निंग के तीन उपयोग लिखें।\n"}, {"instruction": "टेक्स्ट के अनुसार, एक एप्लिकेशन विकसित करने के लिए एक जीपीटी मॉडल का उपयोग करने के तीन लाभ क्या हैं?\nफिर मॉडल द्वारा उत्पन्न कोड एक ऐप में शामिल किया जा सकता है, जिसे ऐप डेवलपर्स द्वारा टेस्ट और संशोधित किया जा सकता है।"}, {"instruction": "कंप्यूटर के सन्दर्भ में 'कैश' का क्या अर्थ होता है?\n"}, {"instruction": "अपने कंप्यूटर को हैक होने से बचाने के दो तरीके बताएं।\n"}, {"instruction": "अलग-अलग परिस्थितियों में लोगों के पांच विषम व्यवहारों की सूची बनाएं।\n"}, {"instruction": "ग्रेविटी वस्तुओं के गति पर कैसे प्रभाव डालती है?\n"}, {"instruction": "एक भाषण को प्रेरक या सूचनात्मक के रूप में वर्गीकृत करें।\nयह भाषण कॉलेज शिक्षा के महत्व के बारे में है।"}, {"instruction": "ऑनलाइन अभियान बनाने के लिए सामग्री विपणनकर्ताओं द्वारा प्रयोग किए जाने वाले मुख्य विभागों की पहचान करें।\n"}, {"instruction": "यह निर्देशक बताता है कि दिया गया बयान अध्ययन का एक अनुमान है या नहीं।\nवायुमंडल में CO2 जलवायु परिवर्तन का एक प्रमुख ड्राइवर है।"}, {"instruction": "दिए गए तथ्यों के एक सेट ले और उससे एक रचनात्मक कहानी बनाएं।\nसूरज अस्त हो रहा था, आसमान गुलाबी था और दूर में एक लाइटहाउस वाली एक समुद्र तट थी।"}, {"instruction": "पांच साल के बच्चे के लिए एक अच्छा उपहार क्या हो सकता है?\n"}, {"instruction": "एक उत्पाद या सेवा दी गई हो तो, समझाएं कि उपभोक्ताओं को उसे कोई दूसरे समान उत्पाद या सेवा की तुलना में पसंद करेंगे।\n"}, {"instruction": "सबसे जटिल से सबसे कम जटिल तक दिए गए आइटमों को श्रेणीबद्ध करें।\nमगरमच्छ, कछुआ, सांप।"}, {"instruction": "एशिया क्षेत्र में देशों के बीच संबंधों का वर्णन करें।\n"}, {"instruction": "मानसिक रूप से स्वस्थ रहने के लिए खुद को संरक्षण करने के तरीके बताएं।\n"}, {"instruction": "एक खुशी का पल बनाएँ।\n"}, {"instruction": "एक औपचारिक ईमेल लिखें जो बोस से अनुरोध करता है कि अगले दिन छुट्टी लेनी है।\nमैं आपको सूचित करना चाहता हूँ कि कल मैं अपनी अगली नौकरी खोजने के लिए घर बैठूंगा। मैं इसलिए कल छुट्टी लेना चाहता हूँ।"}, {"instruction": "महान नेताओं के कौन से गुण होते हैं?\n"}, {"instruction": "दो लोगों के लिए एक सड़क यात्रा के लिए बजट बनाएं।\nगंतव्य: सैन फ्रांसिस्को, लॉस एंजल्स, लास वेगास"}, {"instruction": "व्यावसायिक को क्यों संतुलित जीवन जीना चाहिए, इसे समझाएं।\n"}, {"instruction": "कंपनियों कैसे पूर्वानुमान विश्लेषण का उपयोग कर सकती हैं, इसका एक उदाहरण दें।\n"}, {"instruction": "आश्चर्य की भावना को व्यक्त करने वाला हाइकू रचिए।\n"}, {"instruction": "एक वाक्य में एक बिल्ली की विशेषताओं का वर्णन करें।\n"}, {"instruction": "कृपया बताएं कि इस वाक्यांश \"Once in a blue moon\" को कैसे वाक्य में उपयोग किया जा सकता है।\n"}, {"instruction": "एक दीवार को सही ढंग से पेंट करने का वर्णन कीजिए।\n"}, {"instruction": "सूची का औसत निकालने के लिए एक फ़ंक्शन उत्पन्न करें।\n"}, {"instruction": "एक ग्राहक के जरूरतों को बेहतर समझने के लिए आप पांच सवाल ब्रेनस्टॉर्म करें जो आप प्रश्न पूछ सकते हैं।\n"}, {"instruction": "कमरे को पेंट करने का तरीका समझाएं।\n"}, {"instruction": "मशीन लर्निंग अनुप्रयोग विकसित करने के लिए सबसे अच्छी प्रोग्रामिंग भाषा कौन सी है?\n"}, {"instruction": "एक खुश धुन वाले और आशापूर्ण भावना व्यक्त करने वाले बोलों से गीत बनाएं।\n"}, {"instruction": "निम्नलिखित कथन के खंडन कीजिए \"पर्यावरण के विकल्प ऊर्जा स्रोतों का इस्तेमाल पैसे की बर्बादी है।\"\n"}, {"instruction": "एक कंप्यूटर प्रोग्राम में एक लाइब्रेरी का उद्देश्य क्या होता है?\n"}, {"instruction": "एक विश्वसनीय डेटा स्रोत सेटअप की प्रक्रिया का वर्णन करें।\n"}, {"instruction": "एक केक बेक करने के लिए एक कदम-दर-कदम गाइड तैयार करें।\n"}, {"instruction": "दो लंबाई 6 की सूचियों को एकल सूची में मिलाएं।\nसूची 1: 1, 3, 5, 7, 9, 11\nसूची 2: 2, 4, 6, 8, 10, 12"}, {"instruction": "दुनिया में घूमने के लिए पांच सबसे अच्छे देशों की सूची बनाएं।\n"}, {"instruction": "एक मेटाफ़ॉर्म के रूप में एक कहानी बनाएं जो बुद्धिमत्ता की अवधारणा को समझाती हो।\n"}, {"instruction": "निम्नलिखित शहरों को स्वास्थ्य सेवाओं की गुणवत्ता के अनुसार श्रेणीबद्ध करें।\nशहर: टोक्यो, एम्स्टर्डाम, मॉस्को"}, {"instruction": "बताएं कि एक विद्युत धारा कहाँ से उत्पन्न होती है।\n"}, {"instruction": "नीचे दिए गए समस्या को हल करने का एक उत्कृष्ट तरीका खोजें।\nबहुत से लोग एक साझा कार्यालय स्थान तक पहुंचते हैं, इसलिए यह हमेशा शोरगुल और ध्यान भटकाने वाला होता है।"}, {"instruction": "दो लीटर में कितने आउंस होते हैं?\n"}, {"instruction": "1 से 5 तक निम्नलिखित वाक्यों को एक तार्किक संरचना में व्यवस्थित करें।\n(1) फिर कार अमेरिका को भेजी गई। (2) कार के पार्ट जापान में बनाए गए थे। (3) इसे इटली में डिजाइन किया गया था। (4) कार जर्मनी में फैक्टरी में असेम्बल की गई थी। (5) एक नई स्पोर्ट्स कार रिलीज की गई थी।"}, {"instruction": "पर्यावरणीय टिकाऊता के महत्व पर एक प्रेरक भाषण के मुख्य बिंदु बताएँ।\n"}, {"instruction": "एक राजकुमार की कहानी बनाएं जो एक दुष्ट ड्रैगन से लड़ने का फैसला लेता है।\n"}, {"instruction": "5 पृष्ठों के एक शोध पत्र लिखने के लिए तीन दिलचस्प विषयों की सूची तैयार करें।\n"}, {"instruction": "एक पड़ोस में अप्रत्यक्ष अपराध की मात्रा का मापने के लिए डेटा एकत्र करें।\n"}, {"instruction": "अग्रिम और प्रतिक्रियात्मक ग्राहक सेवा के बीच अंतर समझाएं।\n"}, {"instruction": "एल्गोरिथ्म्स के बारे में एक रैप रचित करें जो अवधारणा को सटीकता से अपनाता हो।\n"}, {"instruction": "फॉसिल ईंधन जलाने की कुछ सामाजिक और पर्यावरणीय प्रभाव क्या हैं?\n"}, {"instruction": "बताएं कि स्वास्थ्य सेवाओं में प्राकृतिक भाषा प्रसंस्करण का उपयोग कैसे किया जाता है।\n"}, {"instruction": "निम्नलिखित कार्य के लिए एक कंप्यूटर दृष्टि मॉडल डिज़ाइन करें।\nएक छवि में वस्तुओं का पता लगाना।"}, {"instruction": "पर्यावरण और टिकाऊता के बीच क्या संबंध है?\n"}, {"instruction": "अध्ययन से शुरू होकर एक कार खरीदने की प्रक्रिया का वर्णन करें:\n"}, {"instruction": "50 सिक्कों के झटके में 35 सिरे पाने की संभावना क्या है?\n"}, {"instruction": "एक लीनियर रीग्रेशन मॉडल के घटक क्या होते हैं?\n"}, {"instruction": "निम्नलिखित कंपनियों में सीएसआर को प्रोत्साहित करने के नीतियों की सूची बनाएं।\n"}, {"instruction": "विकासशील देशों में शिक्षा का महत्व वर्णन करें।\n"}, {"instruction": "यूरोपीय संघ (EU) ने 2016 के ब्रेक्सिट रेफरेंडम का कैसे प्रतिक्रिया दी?\n"}, {"instruction": "एक विशेष कंपनी के लिए स्टॉक के व्यवहार का विश्लेषण करें।\nमाइक्रोसॉफ्ट कॉर्पोरेशन"}, {"instruction": "एक परिभाषा दी गई है, शब्द के सही वर्तनी को पहचानें।\nएक विशिष्ट रूप, आकृति या संयोजन में तत्वों का व्यवस्थापन:"}, {"instruction": "एक तर्क बनाएं जो मुझे एक कार खरीदने के लिए दलील देने में सक्षम हो।\n"}, {"instruction": "वर्चुअल प्राइवेट नेटवर्क (VPN) क्या है?\n"}, {"instruction": "पानी के दो रासायनिक घटक क्या हैं?\n"}, {"instruction": "दिए गए खाद्य का स्वाद प्रोफ़ाइल वर्णन करें।\nस्ट्रॉबेरी आइसक्रीम"}, {"instruction": "\"मेलंकोली\" शब्द का उपयोग कहाँ हो सकता है, उसका एक उदाहरण दीजिए।\n"}, {"instruction": "एक नए सॉफ्टवेयर की मुख्य विशेषताओं को उजागर करते हुए कुछ वाक्य लिखें।\n"}, {"instruction": "एचवीएस सिस्टम में एक एयर फिल्टर का उद्देश्य क्या होता है?\n"}, {"instruction": "यूनाइटेड किंगडम कौन सी मुद्रा का उपयोग करता है?\n"}, {"instruction": "\"एक पैसा बचाया एक पैसा कमाया जाता है\" को कैसे प्रश्नात्मक रूप में बदलें?\n"}, {"instruction": "वेबसाइट के लिए एक चैटबॉट बनाएं।\n"}, {"instruction": "किसी ऐसे परिदृश्य को समझाएं जब कृत्रिम बुद्धिमत्ता सर्वोत्तम समाधान नहीं हो सकती।\n"}, {"instruction": "जानें कि संयुक्त राज्य अमेरिका के राष्ट्रपति कौन है।\n"}, {"instruction": "निम्नलिखित प्रोग्रामिंग भाषा के लिए, जीपीटी मॉडल को पूरा करने के लिए एक कार्य सुझाएं।\nनिम्नलिखित कार्य Python भाषा के लिए है।"}, {"instruction": "दिए गए सामग्री को शामिल करने वाली एक व्यंजन बनाएँ।\nआलू और गाजर"}, {"instruction": "\"जॉन दौड़ा और दौड़ा, जब तक वो दौड़ नहीं पा रहा था।\"\n"}, {"instruction": "ईमेल सूची में डुप्लिकेट संपर्क प्रविष्टियों की खोज के लिए एक एल्गोरिथ्म डिजाइन करें।\n"}, {"instruction": "एक उष्णकटिबंधीय समुद्र तट के बारे में कुछ लाइन लिखें।\n"}, {"instruction": "\"अपने अंडे मत गिनो पहले\" उपमा के अर्थ समझाएं।\n"}, {"instruction": "दिए गए शीर्षक के आधार पर, कागज का सारांश बनाएं।\nस्वास्थ्य सेवाओं में मशीन लर्निंग का उपयोग"}, {"instruction": "एक ग्राफिक डिजाइनर से साक्षात्कार करते समय पूछने के लिए प्रश्नों की सूची संश्लेषित करें।\n"}, {"instruction": "मछलियों को अंशकालिक, शाकाहारी या सर्वाहारी के रूप में वर्गीकृत करें।\nको अंशकालिक, शाकाहारी या सर्वाहारी के रूप में वर्गीकृत करें।"}, {"instruction": "कहानी का थीम तय करें।\nयह एक युवा लड़की की कहानी है जो स्कूल से घर वापस लौटते समय बर्फबारी में फंस जाती है।"}, {"instruction": "दिए गए वाक्य में समझ को बढ़ाने के लिए एक उपमा जोड़ें।\nउसमें थकान की निशानियां दिखाई देने लगी थी।"}, {"instruction": "स्टार वॉर्स फिल्म के बारे में मुझे बताएं।\n"}, {"instruction": "कर्मचारी मनोबल को बढ़ाने के लिए चार विचार प्रदान करें।\n"}, {"instruction": "एक दिए गए एल्गोरिदम को लागू करें।\nबाइनरी सर्च"}, {"instruction": "कर्मचारी की अवकाश और छुट्टियों की रिकॉर्ड रखने के लिए एक डेटाबेस डिजाइन करें।\n"}, {"instruction": "वाक्य को पुनः गठित करके उल्टा संदेश पहुँचाएं।\nवह नौकरी के लिए योग्य नहीं है।"}, {"instruction": "एक दिए गए जीव प्रजाति के लिए आदर्श तापमान निर्धारित करें।\nपशु जाति: बाल्ड ईगल्स"}, {"instruction": "कारोट आपके लिए क्यों अच्छे होते हैं, इसे समझाएं।\n"}, {"instruction": "दी गई स्थिति में हुए परिवर्तनों का वर्णन करें।\n<सूरज अस्त होते हुए समुद्र तट का एक तस्वीर>"}, {"instruction": "दो लोगों के बीच एक वैज्ञानिक घटना पर चर्चा कर रहे हैं।\nदो लोगों के बीच एक वैज्ञानिक घटना पर चर्चा कर रहे हैं।\\हाइड्रोपावर।"}, {"instruction": "तीन आर्टिफिशियल इंटेलिजेंस अनुप्रयोगों का नाम बताएं।\n"}, {"instruction": "रिपोर्ट के लिए एक सारांश तालिका बनाएँ।\n"}, {"instruction": "मेमोरी को सुधारने के तरीकों की सूची बनाएं।\n"}, {"instruction": "बाहरी वातावरण के बारे में एक पहेली बनाएँ।\n"}, {"instruction": "सोफोकलीज़ द्वारा ओइडीपस रेक्स में प्रमुख थीम्स का विवरण दें।\n"}, {"instruction": "एक सफल मोबाइल एप्लिकेशन के लिए क्या मूल आवश्यकताएं होती हैं?\n"}, {"instruction": "इस वक्तव्य के नैतिक परिणामों का विश्लेषण करें।\n\"अंत माध्यमों को ज्यादा महत्व देने वाला नारा\""}, {"instruction": "एक उपयुक्त सेवा उपलब्धि बनाएँ।\nवेबसाइट रखरखाव"}, {"instruction": "प्रोजेक्ट का प्रबंधन के लिए एक नियम बताएं।\n"}, {"instruction": "\"उसे हमेशा सहायताकारी रहने के लिए इस वाक्य को संपादित करें।\"\nवह हमेशा सहायताकारी रहता था।"}, {"instruction": "अकाउंट के लिए एक पासवर्ड बनाएँ जो कम से कम 8 वर्ण हों, 1 अपरकेस अक्षर, 1 लोअरकेस अक्षर और 2 नंबर हों।\n"}, {"instruction": "मैंने कंप्यूटर को रीस्टार्ट किया।\n"}, {"instruction": "इस अनुक्रम में अगली संख्या क्या होगी?\n2, 6, 14, 30 के बाद अगली संख्या क्या होगी?"}, {"instruction": "दिए गए व्यापार निर्णय में शामिल होने वाले हितधारकों की पहचान करें।\nकंपनी अपनी आईटी विभाग को आउटसोर्स करने की विचारधारा बना रही है।"}, {"instruction": "ग्राहक सेवा ईमेल का एक उदाहरण प्रदान करें।\n"}, {"instruction": "SQL अधिषठान हमलों से सुरक्षा प्रतिक्रिया सुझाएं।\n"}, {"instruction": "संदेश को एक गुप्त कोड में बदलें।\nमीटिंग 10 बजे को स्थानांतरित की गई है।"}, {"instruction": "बताओ कि एक विद्युत क्षेत्र कैसे उत्पन्न होता है।\n"}, {"instruction": "निम्नलिखित वाक्य को इस तरह से फिर से शब्दों का चुनाव करें कि जोर \"गायक\" शब्द पर रहे।\nप्रसिद्ध गायक कल रात पट्टी पर उतरे।"}, {"instruction": "'प्राकृतिक आपदा' पर आधारित कहानी के लिए तीन मुख्य विचार उत्पन्न करें।\n"}, {"instruction": "वेबसाइट डिज़ाइन के लिए ऑनलाइन उपकरण सुझाएं।\n"}, {"instruction": "एक कक्षा का खुलासा कीजिए।\n"}, {"instruction": "एक नए लक्जरी कार के लिए एक विज्ञापन नारा बनाएँ।\n"}, {"instruction": "निम्नलिखित वाक्य को संशोधित करें ताकि यह अधिक स्पष्ट हो जाए।\nऐप आपको ध्यान करने में मदद करेगा और अधिक ध्यानवान बनाएगा।"}, {"instruction": "दिए गए समस्या-समाधान टास्क में अगला कदम सुझाव दें।\nएक व्यक्ति कोड तोड़ने का प्रयास कर रहा है लेकिन उसे फंस गया है।"}, {"instruction": "इस लिंक को खोलें और पहली वाक्य को संपादित करें।\nhttps://en.wikipedia.org/wiki/The_Great_Gatsby"}, {"instruction": "नींबू का रस और अदरक से बने दो नाश्तेदार शराबों के नाम बताओ।\n"}, {"instruction": "एक कहानी लिखें जिसमें एक लोमड़ी और एक भालू मित्र बन जाते हैं।\n"}, {"instruction": "इस तरह के व्यंजन के लिए एक सामान्य सामग्री का नाम बताएं।\nसुशी"}, {"instruction": "एक प्रोग्रामर भूमिका के लिए साक्षात्कार में पूछने के लिए प्रश्नों का एक सेट बनाएं।\n"}, {"instruction": "एक सफल टीमवर्क के बारे में ट्वीट लिखें।\n"}, {"instruction": "इन पाँच ऑब्जेक्ट्स को एक अर्थपूर्ण श्रेणी में वर्गीकृत करें।\nपेंसिल, कागज, रबड़, सीधी, किताब"}, {"instruction": "एक वाक्य दिया गया है, जिसमें सुझाव दिया गया है जो अधिक उपयुक्त है।\nइवेंट पूरी तरह से विफल रहा।"}, {"instruction": "\"मैं बस यह देख रहा हूं कि आप अभी भी ज़िंदा हैं\" के स्पीच एक्ट को वर्गीकृत करें।\n"}, {"instruction": "दी गई प्रोग्रामिंग भाषा की सिंटेक्स का विवरण दें।\nPython"}, {"instruction": "निम्नलिखित मशीन लर्निंग एल्गोरिथ्म के लिए एक वाक्यांश परिभाषा प्रदान करें।\nनैव बीज़ वाक्यांश"}, {"instruction": "जनसंख्या घनत्व और जनसंख्या वृद्धि के बीच अंतर को समझाएं।\n"}, {"instruction": "निम्नलिखित वाक्य में एक अक्षर भूल है, उसे ढूंढें और सही शब्द दें।\nयह एक अविश्वसनीय अनुभव था।"}, {"instruction": "दो चरों के बीच संबंध खोजें।\nऊंचाई और वजन"}, {"instruction": "इस पहेली का एक हल बताएँ: दो बिल्लियाँ एक कमरे में हैं। उनमें से एक सफेद है और दूसरी काली है। आप उन्हें ध्वनि या दृश्य पर भरोसा न करते हुए उन्हें कैसे अलग करेंगे?\n"}, {"instruction": "दुनिया का पहला 3 डी प्रिंटिंग पेन क्या है?\n"}, {"instruction": "सूचना प्रौद्योगिकी से संबंधित एक बजवर्ड ढूंढें।\n"}, {"instruction": "एक गुलाब के पौधे की देखभाल करने का सबसे अच्छा तरीका क्या है?\n"}, {"instruction": "किसी के चालाकी का एक उदाहरण दें।\n"}, {"instruction": "कुछ कंप्यूटर वायरसों का नाम बताएं।\n"}, {"instruction": "वर्चुअल रिऐलिटी प्रौद्योगिकी में वर्तमान की रुझानों का अनुसंधान करें।\n"}, {"instruction": "आपको निम्नलिखित वाक्य दिया गया है। दिए गए शब्द के लिए सबसे उपयुक्त पर्यायवाची प्रदान करें।\nमुर्गी मसालों से तैयार की गई थी और आत्यधिक तरीके से पकाई गई थी।"}, {"instruction": "एक यादगार ब्रांड पहचान बनाने का एक तरीका सुझाएं।\n"}, {"instruction": "निम्नलिखित समीकरण लीजिए और बाटने का नियम उपयोग कर एक समकक्ष रूप में उसे बदल दीजिए।\n2x + 4y + 5z = 7"}, {"instruction": "बताएं कि एक व्यक्ति जब एक एनर्जी ड्रिंक पीता है, तो उसे कैसा महसूस होगा?\n"}, {"instruction": "कक्षा में तकनीक का उपयोग करने के 5 रचनात्मक तरीकों की सूची उत्पन्न करें।\n"}, {"instruction": "दिए गए वाक्य को ठीक करने और व्याकरणिक रूप से सही बनाने के लिए सबसे अच्छा तरीका क्या होगा?\nवह स्कूल में सबसे तेज दौड़ने वाली हैं।"}, {"instruction": "एक महाभूलभुलैया डिजाइन करें जिसमें चार कमरे हों जिनमें से प्रत्येक कमरे में एक प्रवेश और निकास द्वार हो।\n"}, {"instruction": "निम्नलिखित गीत बोलों के अर्थ समझें।\nमैं समुद्र से समुद्र तक खोजा, पर आराम करने के लिए कोई जगह नहीं मिली।"}, {"instruction": "एक कॉमिक स्ट्रिप डिज़ाइन करें जो पुनर्चक्रण के महत्व के बारे में हो।\n"}, {"instruction": "निम्नलिखित ब्रांड दिशानिर्देशों के अनुसार एक लोगो डिज़ाइन करें।\nब्रांड नाम: द ग्रैंड होटेल\nब्रांड रंग: नेवी ब्लू और सुनहरा\nफ़ॉन्ट: क्वाट्रोसेंटो सैन्स\nटैग लाइन: \"लक्जरी का अनुभव करें\""}, {"instruction": "एक पोस्टर के लिए डिजाइन तैयार करें।\nविषय: जलवायु परिवर्तन"}, {"instruction": "अलंकार का एक उदाहरण बनाएं।\n"}, {"instruction": "दी गई वेब पृष्ठ से विषय संबंधी जानकारी प्राप्त करें।\nhttps://www.theguardian.com/world/2020/may/14/coronavirus-latest-news-covid-19-updates-south-asia-india-bangladesh"}, {"instruction": "निम्नलिखित समस्या को हल करें\nएक टैंक में 3 लीटर पानी है। दो पाइप हैं। पहले पाइप से 5 मिनट में 9 लीटर पानी भर सकती है जबकि दूसरी पाइप से इसे 7 मिनट में खाली कर सकती है।"}, {"instruction": "एक संख्याओं की सूची दी गई है, दिए गए लक्ष्य संख्या के बराबर अंतर वाले सभी जोड़ों को ढूंढें।\nसूची: {7, 4, 2, 1, 9, 8}\nलक्ष्य संख्या: 4"}, {"instruction": "दो दोस्तों की एक कहानी उत्पन्न करें, जो एक नए व्यक्ति से मिलते हैं।\n"}, {"instruction": "1950 के दो क्लासिक फिल्मों का नाम बताएं?\n"}, {"instruction": "टेनिस मैच का विवरण दीजिए।\n"}, {"instruction": "अंतरराष्ट्रीय अपराधी न्यायालय का क्या भूमिका है?\n"}, {"instruction": "विश्लेषण करें कि इस चित्र का कैसा प्रभाव है।\nhttps://upload.wikimedia.org/wikipedia/commons/f/fa/No_ID_by_<PERSON>_<PERSON>_<PERSON>.jpg"}, {"instruction": "एक काल्पनिक प्राणी के लिए एक नाम उत्पन्न करें।\n"}, {"instruction": "एक मोबाइल ऐप बनाएं जो लोकप्रिय खेल परिणाम दिखाता है।\n"}, {"instruction": "भौतिक सामग्री की गति पर गुरुत्व ने क्या प्रभाव डालता है?\n"}, {"instruction": "$3000 के बजट पर सान फ्रांसिस्को के लिए 5 दिन की छुट्टी की लागत अनुमान लगाएं।\n"}, {"instruction": "स्थायी विकास के बारे में जागरूकता बढ़ाने के लिए एक खेल डिजाइन करें।\n"}, {"instruction": "ऑटोट्रोफिक जीवाणु ऊर्जा कैसे प्राप्त करता है?\n"}, {"instruction": "सिंडरेला के काँच के जूते का एक आधुनिक संस्करण डिजाइन करें।\n"}, {"instruction": "एक ऐसा एल्गोरिथम बनाएं जो दिए गए एरे से डुप्लिकेट को हटा देता हो।\narr = [1, 2, 2, 3, 4, 4, 5]को हटाएं।"}, {"instruction": "निम्नलिखित सिंथजिस के लिए एक किताब का शीर्षक खोजें।\nएक युवा लड़की संघर्ष करती है सभी समस्याओं के खिलाफ अपने परिवार की मदद करने और एक खतरनाक दुनिया में खुशी ढूंढने के लिए।"}, {"instruction": "वेब क्रॉलर क्या करता है उसे समझाएं।\n"}, {"instruction": "दी गई कहानी के मुख्य अवधारणा का उदाहरण दें।\nएक मत्स्यकन्या की कहानी जो जमीन की खोज करती है।"}, {"instruction": "अगले दो दिनों के लिए मौसम का अनुमान लिखें।\n"}, {"instruction": "इस कार्य में, आपको बताना होगा कि निम्नलिखित वाक्य में वर्णित व्यक्ति की गुणवत्ता क्या है।\nउसने उन लोगों की मदद करने के लिए अपना समय समर्पित किया जो उससे कम सौभाग्यशाली थे।"}, {"instruction": "एक जेनरेटर के उद्देश्य का विवरण दें।\n"}, {"instruction": "2021 के अनुसार सबसे लोकप्रिय प्रोग्रामिंग भाषा कौन सी है?\n"}, {"instruction": "एक स्ट्रिंग ऑफ़ नंबर्स को इंटीजर में रूपांतरित करने के लिए एक फ़ंक्शन उत्पन्न करें।\n\"123456\""}, {"instruction": "एक नौकरी खोज के लिए एक उपयुक्त रूपक बनाएँ।\n"}, {"instruction": "एक सफल उद्यमी की पांच महत्वपूर्ण विशेषताएं सूचीबद्ध करें।\n"}, {"instruction": "एक रोबोट जो एक द्वीप पर फंस गया हो उसके बारे में एक कहानी सुनाओ।\n"}, {"instruction": "निम्नलिखित दो शब्दों को मिश्रण बनाकर एक नया शब्द बनाएँ: 'साझा', 'सर्फ', 'जुड़ें'.\n"}, {"instruction": "पर्यावरण को संरक्षित और संरक्षित रखने का सबसे अच्छा तरीका वर्णित करें।\n"}, {"instruction": "वाक्य में उचित सम्बन्धबोधक शब्द डालें।\nमैं दुकान तक चला गया था और कुछ खाने की चीजें खरीदी।"}, {"instruction": "एक मानव को ऑक्सीजन सांस लेने की क्यों जरूरत होती है?\n"}, {"instruction": "SDLC के पांच चरण क्या हैं?\n"}, {"instruction": "टीम विकास के पांच चरणों का नाम बताएं।\n"}, {"instruction": "दिए गए परियोजना के लिए आवश्यक सामग्री प्रदान करें।\nएक चिड़ियाघर बनाएं।"}, {"instruction": "दो व्यक्तियों के बीच एक संवाद उत्पन्न करें जो कृत्रिम बुद्धिमत्ता के विषय पर चर्चा कर रहे हैं।\n"}, {"instruction": "'Let the cat out of the bag' के मुहावरे का अर्थ समझाएँ।\n\"Let the cat out of the bag\" का मतलब है कोई रहस्य या गोपनीय सच्चाई बता देना।"}, {"instruction": "घटकों का विवरण दें जो पुनरावर्ती डिजाइन प्रक्रिया के महत्वपूर्ण अंग होते हैं?\n"}, {"instruction": "कक्षा वातावरण को सुधारने के लिए 3 विवरणों के साथ 2 विचारों को उत्पन्न करें।\n"}, {"instruction": "एक आम हाई स्कूल के छात्र के जीवन का विवरण दीजिए।\n"}, {"instruction": "दिए गए प्रम्प्ट के आधार पर एक अद्वितीय कवितात्मक प्रतिक्रिया उत्पन्न करें।\nप्रम्प्ट: रात में सितारे चमकते हैं।"}, {"instruction": "एक ग्राहक और एक स्टोर क्लर्क के बीच एक नया फोन खरीदने के बारे में बातचीत के लिए उचित संवाद बनाएं।\n"}, {"instruction": "1 से 10 तक के नंबरों को प्रिंट करने वाला एक छोटा प्रोग्राम लिखें।\n"}, {"instruction": "मेरे पास क्या करना होगा उसे मैं नहीं जानता था।\nमुझे पता नहीं था कि क्या करना है।"}, {"instruction": "वाक्य शामिली क्या होती है?\n"}, {"instruction": "क्रिया को पैशन में से एक्टिव में बदलें:\nकल नया उत्पाद लॉन्च किया गया था।\n"}, {"instruction": "\"Jury\" शब्द, \"verdict\" और \"dissertation\" शब्दों के साथ एक वाक्य बनाएँ।\n"}, {"instruction": "एक वैज्ञानिक की कहानी लिखें जो एक ग्रह की खोज करता है।\n"}, {"instruction": "प्रभाव का बयान करें कि पौधे जल का उपभोग कैसे करते हैं।\n"}, {"instruction": "निम्नलिखित वस्तु के निर्माण में शामिल विधि का विवरण दें।\nआर्क पुल"}, {"instruction": "एक उदाहरण दिया गया है, पिछले राष्ट्रपति चुनाव में कितने लोगों ने वोट डाला था?\n"}, {"instruction": "कुशलता बढ़ाने के लिए दो विधियों की पहचान करें।\n"}, {"instruction": "गूगल अकाउंट बनाने के लिए आपको क्या करना होगा?\n"}, {"instruction": "एक रोबोट के बारे में एक छोटी सी कहानी लिखें जो शहर में गुम हो जाता है।\n"}, {"instruction": "एक प्राकृतिक आपदा के मामले में शक्तिशाली सुरक्षा प्रक्रियाओं और सलाह की सूची बनाएँ।\nप्राकृतिक आपदा: फ्लैश बाढ़"}, {"instruction": "निम्नलिखित वाक्य को छोटा करने के लिए एक तरीका ढूंढें लेकिन अभी भी उसी अर्थ को प्रदर्शित करें।\nमुझे इस कार्य को करना जरूरी होता है क्योंकि यह महत्वपूर्ण है।"}, {"instruction": "दो चरित्रों के बीच एक बातचीत उत्पन्न करें जिससे निम्नलिखित कथन का व्याख्या की जाए।\nसैम को बहुत अधिक आश्वस्त लग रहा था क्योंकि उसे मालूम था कि वह गलत था।"}, {"instruction": "AI का उपयोग तरफ़दारी और भेदभाव को कम करने के लिए कैसे किया जा सकता है?\n"}, {"instruction": "रोमांटिक कॉमेडी ड्रामा के लिए एक फिल्म का शीर्षक सुझाएं।\n"}, {"instruction": "एक प्रकार के पक्षी के दो उदाहरण दें।\n"}, {"instruction": "दो रंगों का चयन करें और बताएं कि वे कौन सी भावनाओं को उत्पन्न करते हैं।\nनीला और पीला।"}, {"instruction": "कुछ ऐसी कंपनी का उदाहरण दें जो स्थायी कृषि के अभ्यास करती है।\n"}, {"instruction": "बहुतों द्वारा जीत की संभावना असंभव मानी जाती थी। (Active Voice में नया वाक्य: बहुतों ने जीतने की संभावना को असंभव माना था।)\n"}, {"instruction": "XYZ के स्टॉक की कीमत कल क्यों बढ़ी, इसे समझाएं।\n"}, {"instruction": "निम्नलिखित कथन को नैतिक या अनैतिक ठहराएँ।\nडेवलपरों को उनके सॉफ्टवेयर उत्पादों की कीमतें निर्धारित करने की अनुमति दी जानी चाहिए।"}, {"instruction": "एक कंपनी के विकास वाक्यांश को दर्शाते हुए एक दृश्य बनाएं।\nडेटा:\n- वर्ष 1 में विकास दर - 20%\n- वर्ष 2 में विकास दर - 30%\n- वर्ष 3 में विकास दर - 40%"}, {"instruction": "योजना की स्थिति का विश्लेषण करें और फैसले के साथ आगे बढ़ना है या नहीं, इस पर निर्णय लें।\nमैं एक नई उत्पाद लॉन्च करने का विचार कर रहा हूं जो उम्मीदवार लगता है। हालांकि, इस उत्पाद की लोकप्रियता न होने की स्थिति में बड़े हानि का जोखिम है।"}, {"instruction": "एक मूलनीति से सम्बंधित कहानी सुनाओ।\n"}, {"instruction": "एक कॉस्मेटिक कंपनी के लिए एक मार्केटिंग योजना विकसित करें।\n"}, {"instruction": "लेखक अध्याय में क्या सबसे महत्वपूर्ण बात बता रहे हैं का पता लगाएँ।\nशहरीकरण शहरों और नागरिकों के लिए कई लाभ लेकर आता है। सुधारी भौमिकी से ट्रांसपोर्टेशन आसान होता है, नौकरियां उत्पन्न होती हैं और आर्थिक लाभ पैदा होता है और सेवाओं और सुविधाओं के विकास को प्रोत्साहित करता है। इससे भी विभिन्न समुदायों के बीच सांस्कृतिक एवं संगठनात्मक विनिमय के अवसर पैदा होते हैं और बेहतर समझ व परस्पर सहयोग को प्रोत्साहित करता है।"}, {"instruction": "दवाई के प्रभाव को स्वास्थ्य परिणामों पर वर्णन करें।\nआइबुप्रोफेन"}, {"instruction": "\"खुश\", \"खुशमिजाज\" और \"आनंदमय\" शब्दों को श्रेणीबद्ध करें।\n"}, {"instruction": "एक शब्दों की सूची दी जाती है, जो आपकी महसूसी वर्तमान मानसिक स्थिति को उठाती होगी, एक कविता बनाएँ।\nशांत, जागरूक, कृतज्ञ"}, {"instruction": "ईमेल संचार को कैसे और अधिक सक्षम बनाया जा सकता है?\n"}, {"instruction": "उन लोगों की मदद करने के लिए एक ऐप डिजाइन करें जो किसी कार्य पर फोकस करने और विभिन्न दिलचस्प शायद आने वाली बातों को टालने में मदद करता है।\n"}, {"instruction": "एक किताबों की सूची दी गई है, उन्हें पांच स्टार स्केल पर मूल्यांकन करें।\n, The Great Gatsby, Pride and Prejudice\n\nड्राकुला, ड्यून, जेन एयर, द ग्रेट गैट्स्बी, प्राइड एंड प्रेज्यूडिस"}, {"instruction": "मानों दूसरे दो गैलेक्सी नाम मिलाओ जो धूमकेतु से अलग हों।\n"}, {"instruction": "ओपन-एंडेड और क्लोज्ड-एंडेड प्रश्नों के बीच अंतर का विवरण दें।\n"}, {"instruction": "5 मशीन लर्निंग सिखाने वाली किताबें सलाह दें।\n"}, {"instruction": "एक वित्तीय उपकरण का उदाहरण प्रदान करें जो निवेश पर लाभ की पहचान करने के लिए उपयोग किया जाता है।\n"}, {"instruction": "तकनीक ने शिक्षा को कैसे प्रभावित किया है, उसे संक्षिप्त में बताएं।\n"}, {"instruction": "इनपुट दिए गए होने पर, तुलना का उपयोग कर एक वाक्य बनाएं।\n"}, {"instruction": "मशीन लर्निंग क्षेत्र में एक वर्तमान रुझान का वर्णन करें।\n"}, {"instruction": "दी गई कंपनी के लिए एक महत्वपूर्ण मूल्य नाम बताओ।\nकंपनी: ग्रीनपीस"}, {"instruction": "बाउंडेड रैशनैलिटी की अवधारणा का विवरण दें।\n"}, {"instruction": "निम्नलिखित वाक्य को सही रूप से नियंत्रित करें:\nजॉन ने मुझसे पूछा कि क्या तुमने अपना काम पूरा किया है?"}, {"instruction": "\"ज्यादा मिठाई खाना अनारोग्यप्रद होता है\" - वाक्य को एक तथ्य या मत के रूप में श्रेणी बदलें:\n"}, {"instruction": "निम्नलिखित नमूना मानों के आधार पर मध्यस्थान की गणना करें।\n{९, ७, ११, ८, १०, ७, -३, ६२१} के आधार पर मध्यस्थान की गणना करें।"}]