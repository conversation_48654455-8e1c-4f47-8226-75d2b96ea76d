#!/usr/bin/env python3
"""
Fine-tuning script for Dhanishta model using GPT-OSS approach with Unsloth optimizations.
Based on OpenAI cookbook and Unsloth best practices.
"""

import torch
import pandas as pd
from datasets import load_dataset, Dataset
from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer, BitsAndBytesConfig
from peft import LoraConfig, get_peft_model, PeftModel
from trl import SFTTrainer, SFTConfig
import os
from huggingface_hub import login

def setup_environment():
    """Setup environment and login to Hugging Face."""
    print("Setting up environment...")
    
    # Login to Hugging Face (uncomment and add your token)
    login(token="hf_MRQdJQJtDAfCkifoRvXEAwGJoWCGUiqjAue")
    
    # Check GPU availability
    if torch.cuda.is_available():
        gpu_stats = torch.cuda.get_device_properties(0)
        print(f"GPU: {gpu_stats.name}")
        print(f"GPU Memory: {round(gpu_stats.total_memory / 1024**3, 2)} GB")
    else:
        print("No GPU detected. Training will be very slow on CPU.")
        return False
    return True

def load_model_and_tokenizer(model_name="openai/gpt-oss-20b"):
    """Load model and tokenizer with standard GPT-OSS approach."""
    print(f"Loading model: {model_name}")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # Load model with quantization
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.bfloat16,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True,
    )
    
    model_kwargs = dict(
        attn_implementation="eager",
        torch_dtype=torch.bfloat16,
        quantization_config=quantization_config,
        use_cache=False,
        device_map="auto",
    )
    
    model = AutoModelForCausalLM.from_pretrained(model_name, **model_kwargs)
    
    # Configure LoRA
    peft_config = LoraConfig(
        r=16,
        lora_alpha=32,
        target_modules="all-linear",
        target_parameters=[
            "7.mlp.experts.gate_up_proj",
            "7.mlp.experts.down_proj",
            "15.mlp.experts.gate_up_proj",
            "15.mlp.experts.down_proj",
            "23.mlp.experts.gate_up_proj",
            "23.mlp.experts.down_proj",
        ],
    )
    model = get_peft_model(model, peft_config)
    
    print(f"Trainable parameters: {model.print_trainable_parameters()}")
    return model, tokenizer

def prepare_dataset(dataset_name="Abhaykoul/Dhanishtha-2.0-SUPERTHINKER", split="train"):
    """Prepare dataset for training."""
    print(f"Loading dataset: {dataset_name}")
    
    # Load the dataset
    dataset = load_dataset(dataset_name, split=split)
    print(f"Dataset size: {len(dataset)}")
    
    # Convert to conversation format
    def generate_conversation(examples):
        instructions = examples["instruction"]
        outputs = examples["output"]
        conversations = []
        
        for instruction, output in zip(instructions, outputs):
            conversations.append([
                {"role": "user", "content": instruction},
                {"role": "assistant", "content": output},
            ])
        return {"conversations": conversations}
    
    # Apply conversation format
    reasoning_conversations_data = generate_conversation(dataset)
    
    # Convert to text format using chat template
    tokenizer = AutoTokenizer.from_pretrained("openai/gpt-oss-20b")
    reasoning_conversations = []
    
    for conv in reasoning_conversations_data["conversations"]:
        text = tokenizer.apply_chat_template(
            conv,
            tokenize=False,
        )
        reasoning_conversations.append(text)
    
    # Create final dataset
    data = pd.Series(reasoning_conversations)
    data.name = "text"
    
    combined_dataset = Dataset.from_pandas(pd.DataFrame(data))
    combined_dataset = combined_dataset.shuffle(seed=3407)
    
    print(f"Final dataset size: {len(combined_dataset)}")
    return combined_dataset

def train_model(model, tokenizer, dataset, output_dir="dhanishta-finetuned"):
    """Train the model using SFTTrainer."""
    print("Starting training...")
    
    # Training configuration
    training_args = SFTConfig(
        learning_rate=2e-4,
        gradient_checkpointing=True,
        num_train_epochs=1,
        logging_steps=1,
        per_device_train_batch_size=2,
        gradient_accumulation_steps=4,
        max_length=2048,
        warmup_ratio=0.03,
        lr_scheduler_type="cosine_with_min_lr",
        lr_scheduler_kwargs={"min_lr_rate": 0.1},
        output_dir=output_dir,
        report_to="none",  # Change to "wandb" for logging
        push_to_hub=False,  # Set to True to push to Hub
        save_strategy="epoch",
        evaluation_strategy="no",  # Set to "steps" for evaluation
        save_total_limit=2,
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        optim="adamw_8bit",
        weight_decay=0.01,
        seed=3407,
    )
    
    # Initialize trainer
    trainer = SFTTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=dataset,
        args=training_args,
        dataset_text_field="text",
    )
    
    # Show memory stats before training
    if torch.cuda.is_available():
        start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024**3, 3)
        max_memory = round(torch.cuda.get_device_properties(0).total_memory / 1024**3, 3)
        print(f"GPU Memory before training: {start_gpu_memory} GB / {max_memory} GB")
    
    # Train the model
    trainer_stats = trainer.train()
    
    # Show final stats
    if torch.cuda.is_available():
        used_memory = round(torch.cuda.max_memory_reserved() / 1024**3, 3)
        print(f"Training completed in {trainer_stats.metrics['train_runtime']:.2f} seconds")
        print(f"Peak GPU memory used: {used_memory} GB")
    
    return trainer, trainer_stats

def save_model(model, tokenizer, output_dir="dhanishta-finetuned", push_to_hub=False, repo_name="your-username/dhanishta-model"):
    """Save the trained model and merge PEFT adapter with base model."""
    print(f"Saving model to {output_dir}")
    
    # Save PEFT adapter locally
    model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Push to Hub if requested
    if push_to_hub:
        print(f"Pushing model to Hub: {repo_name}")
        
        # Merge PEFT adapter with base model for 16-bit push
        print("Merging PEFT adapter with base model...")
        merged_model = model.merge_and_unload()
        
        # Push merged model as 16-bit (private)
        print(f"Pushing merged 16-bit model to Hub: {repo_name}")
        merged_model.push_to_hub(repo_name, tokenizer=tokenizer, max_shard_size="5GB", private=True)
        
        # Also save merged model locally
        merged_model.save_pretrained(f"{output_dir}_merged", max_shard_size="5GB")
        tokenizer.save_pretrained(f"{output_dir}_merged")
        print(f"Merged model saved locally to {output_dir}_merged")
    
    print("Model saved successfully!")

def test_inference(model, tokenizer, test_prompts=None):
    """Test the trained model with sample prompts."""
    print("Testing inference...")
    
    if test_prompts is None:
        test_prompts = [
            "Who is Abhay koul?",
            "What is the weather like today?",
            "Can you help me solve a math problem?",
        ]
    
    for prompt in test_prompts:
        print(f"\n--- Testing: {prompt} ---")
        
        # Prepare messages
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        # Generate response
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
        )
        
        # Generate with streaming
        streamer = TextStreamer(tokenizer, skip_prompt=True)
        _ = model.generate(
            **tokenizer(text, return_tensors="pt").to(model.device),
            max_new_tokens=256,
            temperature=0.7,
            top_p=0.8,
            top_k=20,
            streamer=streamer,
            do_sample=True,
        )
        print("\n" + "="*50)

def main():
    """Main training function."""
    print("Starting Dhanishta model training...")
    
    # Setup environment
    if not setup_environment():
        print("Environment setup failed. Exiting.")
        return
    
    # Configuration
    MODEL_NAME = "openai/gpt-oss-20b"  # or your custom model
    DATASET_NAME = "Abhaykoul/Dhanishtha-2.0-SUPERTHINKER"
    OUTPUT_DIR = "dhanishta-finetuned"
    
    try:
        # Load model and tokenizer
        model, tokenizer = load_model_and_tokenizer(MODEL_NAME)
        
        # Prepare dataset
        dataset = prepare_dataset(DATASET_NAME)
        
        # Train model
        trainer, _ = train_model(model, tokenizer, dataset, OUTPUT_DIR)
        
        # Save model
        save_model(
            model, 
            tokenizer, 
            OUTPUT_DIR,
            push_to_hub=True,  # Set to True to push to Hub
            repo_name="Abhaykoul/dhanishta-model"
        )
        
        # Test inference
        test_inference(model, tokenizer)
        
        print("Training completed successfully!")
        
    except Exception as e:
        print(f"Training failed with error: {e}")
        raise

if __name__ == "__main__":
    main()
