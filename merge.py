
import os
import json
from DATASET import DatasetBuilder

def merge_output_chunks(start_idx=0, end_idx=70, processed_dir="processed", output_file="dataset.json"):
    merged_data = []
    base_dir = os.path.dirname(os.path.abspath(__file__))
    processed_path = os.path.join(base_dir, processed_dir)
    for i in range(start_idx, end_idx + 1):
        chunk_file = os.path.join(processed_path, f"output_chunk_{i}.json")
        if os.path.exists(chunk_file):
            with open(chunk_file, 'r', encoding='utf-8') as f:
                try:
                    data = json.load(f)
                    if isinstance(data, list):
                        merged_data.extend(data)
                    else:
                        merged_data.append(data)
                except json.JSONDecodeError as e:
                    print(f"Error decoding {chunk_file}: {e}")
        else:
            print(f"File not found: {chunk_file}")
    # Save merged data using DatasetBuilder for consistency
    builder = DatasetBuilder(filepath=output_file)
    builder.dataset = merged_data
    builder.save_dataset()
    print(f"Merged {len(merged_data)} items into {output_file}")

if __name__ == "__main__":
    merge_output_chunks()
