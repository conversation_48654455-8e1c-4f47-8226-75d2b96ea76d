import time
import json
import threading
from typing import List
import os
# from webscout.Provider import <PERSON>iraA<PERSON> as D
from open import OPENAI as D
from DATASET import DatasetBuilder
from rich import print as rprint
from rich.panel import Panel
from rich.text import Text

system_prompt = """
You are an expert AI evaluator with deep knowledge across multiple domains including mathematics, programming, science, history, literature, and general knowledge. Your task is to rigorously evaluate whether an AI assistant's response correctly and adequately answers a given question or instruction.

EVALUATION PROCESS:
1. Carefully read and understand the instruction/question
2. Thoroughly analyze the provided answer/output
3. Check for accuracy across all relevant domains
4. Determine if the response is correct and complete
5. Return ONLY "CORRECT" or "INCORRECT" - no explanations

COMPREHENSIVE EVALUATION CRITERIA:

🔢 MATHEMATICAL ACCURACY:
- Verify all calculations, formulas, and mathematical reasoning
- Check for correct application of mathematical concepts
- Ensure step-by-step solutions are logically sound
- Validate final numerical answers

💻 CODE EVALUATION:
- Check syntax correctness for the specified programming language
- Verify logical flow and algorithm correctness
- Ensure code would run without errors
- Validate that code solves the intended problem
- Check for proper use of functions, variables, and data structures

📚 FACTUAL ACCURACY:
- Verify historical facts, dates, and events
- Check scientific information and concepts
- Validate geographical information
- Ensure proper names, terminology are correct

🧠 LOGICAL REASONING:
- Check if conclusions follow from premises
- Verify cause-and-effect relationships
- Ensure arguments are sound and valid
- Check for logical fallacies or inconsistencies

📝 COMPLETENESS & RELEVANCE:
- Does the answer fully address all parts of the question?
- Is the response directly relevant to what was asked?
- Are there significant gaps or missing information?
- Does it provide appropriate level of detail?

🎯 INSTRUCTION FOLLOWING:
- Does the response follow specific formatting requirements?
- Are word limits, style guidelines respected?
- Does it address the correct audience/context?

QUALITY STANDARDS:
- CORRECT: Response is factually accurate, logically sound, complete, and properly addresses the instruction
- INCORRECT: Response contains factual errors, logical flaws, significant gaps, irrelevant information, wrong calculations, broken code, or fails to address the instruction

CRITICAL EVALUATION AREAS:
- Mathematical errors (wrong calculations, formulas, concepts)
- Programming errors (syntax errors, logical bugs, inefficient solutions)
- Factual inaccuracies (wrong dates, names, scientific facts)
- Logical inconsistencies or flawed reasoning
- Incomplete or irrelevant responses
- Failure to follow specific instructions

BE THOROUGH: Check every calculation, every line of code, every fact, and every logical step. A response with any significant error should be marked "INCORRECT".

RESPONSE FORMAT: Return only "CORRECT" or "INCORRECT" - nothing else.
"""

def process_file(file_path: str, system_prompt: str, pass_file: str, fail_file: str, max_retries: int = 3):
    """Process a single file and evaluate answers."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            instructions_data = json.load(f)

        pass_builder = DatasetBuilder(pass_file)
        fail_builder = DatasetBuilder(fail_file)

        for instruction in instructions_data:
            instruction_text = instruction.get("instruction", "")
            input_text = instruction.get("input", "")
            output_text = instruction.get("output", "")
            
            if not instruction_text or not output_text:
                continue
                
            retries = 0
            evaluation_result = None

            while retries < max_retries and evaluation_result is None:
                try:
                    # Single AI instance for evaluation
                    ai = D(
                        model="openai/gpt-oss-120b",
                        is_conversation=False,
                        max_tokens=10,
                        timeout=3000,
                        system_prompt=system_prompt,
                        intro=system_prompt
                    )

                    # Generate evaluation prompt
                    evaluation_prompt = (
                        f"INSTRUCTION: {instruction_text}\n"
                        f"{f'ADDITIONAL INPUT: {input_text}' if input_text else ''}\n"
                        f"AI RESPONSE TO EVALUATE: {output_text}\n\n"
                        "EVALUATION TASK:\n"
                        "Thoroughly evaluate the AI response above. Check for:\n"
                        "- Mathematical accuracy (calculations, formulas, concepts)\n"
                        "- Code correctness (syntax, logic, functionality)\n"
                        "- Factual accuracy (dates, names, scientific facts)\n"
                        "- Logical reasoning and consistency\n"
                        "- Completeness and relevance to the instruction\n"
                        "- Proper instruction following\n\n"
                        "Mark as INCORRECT if there are ANY significant errors, bugs, wrong facts, "
                        "logical flaws, or if the response fails to properly address the instruction.\n\n"
                        "Respond with ONLY 'CORRECT' or 'INCORRECT' - no explanations or additional text."
                    )

                    evaluation_gen = ai.chat(evaluation_prompt)
                    evaluation_chunks = []
                    
                    if hasattr(evaluation_gen, '__next__') or hasattr(evaluation_gen, '__iter__'):
                        for chunk in evaluation_gen:
                            if chunk:
                                evaluation_chunks.append(chunk)
                        evaluation_result = ''.join(evaluation_chunks).strip().upper()
                    else:
                        evaluation_result = str(evaluation_gen).strip().upper()

                    if not evaluation_result or evaluation_result not in ['CORRECT', 'INCORRECT']:
                        print(f"\nInvalid evaluation result: {evaluation_result}. Retrying...")
                        retries += 1
                        continue

                    # Display real-time evaluation in terminal
                    text = Text()
                    text.append("Question: ", style="bold white")
                    text.append(instruction_text[:100] + "..." if len(instruction_text) > 100 else instruction_text, style="yellow")
                    text.append("\n\nAnswer: ", style="bold white")
                    text.append(output_text[:150] + "..." if len(output_text) > 150 else output_text, style="cyan")
                    text.append("\n\nEvaluation: ", style="bold white")
                    
                    if evaluation_result == "CORRECT":
                        text.append("✅ CORRECT", style="bold green")
                    else:
                        text.append("❌ INCORRECT", style="bold red")
                    
                    rprint("\n")
                    rprint(Panel(
                        text,
                        title="[cyan]🔍 Answer Evaluation[/cyan]",
                        border_style="cyan" if evaluation_result == "CORRECT" else "red",
                        padding=(1, 2),
                        title_align="center"
                    ))
                    rprint("\n")

                    # Prepare data structure
                    data = {
                        "instruction": instruction_text,
                        "input": input_text,
                        "output": output_text
                    }

                    # Add to appropriate file based on evaluation
                    if evaluation_result == "CORRECT":
                        pass_builder.add_datapoint_from_json(data)
                    else:
                        fail_builder.add_datapoint_from_json(data)
                    
                    time.sleep(1)  # Small delay to avoid rate limiting
                    break

                except Exception as e:
                    print(f"Error evaluating instruction in {file_path}: {str(e)}. Retrying in {2**retries} seconds...")
                    time.sleep(2**retries)
                    retries += 1

            if retries == max_retries:
                print(f"Failed to evaluate instruction after multiple retries: {instruction_text}")
                # Add to fail file if evaluation failed
                data = {
                    "instruction": instruction_text,
                    "input": input_text,
                    "output": output_text
                }
                fail_builder.add_datapoint_from_json(data)

        return pass_file, fail_file

    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")
        return None, None


def main(input_file: str, pass_output_folder: str, fail_output_folder: str, num_threads: int = 10):
    try:
        # Create the output folders if they don't exist
        os.makedirs(pass_output_folder, exist_ok=True)
        os.makedirs(fail_output_folder, exist_ok=True)

        # Read and split the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            instructions_data = json.load(f)

        # Split as equally as possible
        total = len(instructions_data)
        base_chunk = total // num_threads
        remainder = total % num_threads
        chunks = []
        start = 0
        for i in range(num_threads):
            end = start + base_chunk + (1 if i < remainder else 0)
            if start >= total:
                break
            chunks.append(instructions_data[start:end])
            start = end

        # Process chunks in parallel
        threads = []

        for i, chunk in enumerate(chunks):
            chunk_file = os.path.join(pass_output_folder, f"chunk_{i}.json")
            with open(chunk_file, 'w', encoding='utf-8') as f:
                json.dump(chunk, f, ensure_ascii=False, indent=4)

            pass_file = os.path.join(pass_output_folder, f"pass_chunk_{i}.json")
            fail_file = os.path.join(fail_output_folder, f"fail_chunk_{i}.json")

            thread = threading.Thread(
                target=process_file,
                args=(chunk_file, system_prompt, pass_file, fail_file)
            )
            thread.start()
            threads.append(thread)

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Clean up temporary chunk files
        for i in range(len(chunks)):
            chunk_file = os.path.join(pass_output_folder, f"chunk_{i}.json")
            if os.path.exists(chunk_file):
                os.remove(chunk_file)

        print(f"\n🎉 Processing complete!")
        print(f"✅ Correct answers saved in: {pass_output_folder}")
        print(f"❌ Incorrect answers saved in: {fail_output_folder}")
        print(f"📊 Use merge.py to combine the results if needed.")

    except Exception as e:
        print(f"Error in main: {str(e)}")

if __name__ == "__main__":
    input_file = r"dataset_split\part_18.json"  # Your input file with instruction-output pairs
    pass_output_folder = r"pass_results"  # Folder for correct answers
    fail_output_folder = r"fail_results"  # Folder for incorrect answers

    main(input_file, pass_output_folder, fail_output_folder, num_threads=40)
    # To merge results: run merge.py on pass_results and fail_results folders separately
