import json
import os
import re

def save_user_input(filename="demo.json"):
    # Check if the file exists and load existing data
    if os.path.exists(filename):
        try:
            with open(filename, 'r', encoding='utf-8') as json_file:
                data = json.load(json_file)
        except json.JSONDecodeError:
            data = []
    else:
        data = []

    while True:
        user_input = input(">>> ")

        if user_input.lower() == 'exit':
            break

        # Remove leading numbers and any following dot or space
        cleaned_input = re.sub(r'^\d+[\.\s]+', '', user_input)

        entry = {
            "input": "",
            "output": "",
            "instruction": cleaned_input
        }

        data.append(entry)

        # Save data in real-time
        with open(filename, 'w', encoding='utf-8') as json_file:
            json.dump(data, json_file, ensure_ascii=False, indent=4)

    print("Data saved to", filename)

if __name__ == "__main__":
    save_user_input()
