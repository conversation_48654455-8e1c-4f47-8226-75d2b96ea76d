import json
import re

def count_think_blocks(text: str) -> int:
    """
    Count the equivalent number of think blocks in the given text.
    - Each <think> tag counts as 1 block
    - Each <tool_call> counts as 2 blocks

    Args:
        text (str): The text to search for <think> and <tool_call> tags

    Returns:
        int: Number of think block equivalents found
    """
    # Count <think> tags (case insensitive)
    think_pattern = r'<think>'
    think_matches = re.findall(think_pattern, text, re.IGNORECASE)
    think_count = len(think_matches)

    # Count <tool_call> tags (case insensitive) - each counts as 2 blocks
    tool_call_pattern = r'<tool_call>'
    tool_call_matches = re.findall(tool_call_pattern, text, re.IGNORECASE)
    tool_call_count = len(tool_call_matches) * 2

    return think_count + tool_call_count

def filter_json_data(input_file: str, output_file: str) -> None:
    """
    Read JSON file and filter out rows based on think block count:
    - Remove rows with 0 think blocks (no <think> or <tool_call> patterns)
    - Remove rows with exactly 1 think block equivalent
    - Keep rows with 2+ think block equivalents
    - <tool_call> counts as 2 think blocks
    Handles both regular JSON arrays and JSONL (line-delimited JSON) formats.

    Args:
        input_file (str): Path to input JSON file
        output_file (str): Path to output JSON file
    """
    try:
        print(f"Reading data from {input_file}...")

        # Read the JSON file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("Successfully loaded JSON array")

        print(f"Total rows loaded: {len(data)}")

        # Filter the data
        filtered_data = []
        removed_count = 0

        for i, row in enumerate(data):
            if i % 1000 == 0:  # Progress indicator for large files
                print(f"Processing row {i}...")

            # Check if the row has an 'output' field
            if isinstance(row, dict) and 'output' in row:
                think_block_count = count_think_blocks(row['output'])

                # Keep rows that have 2+ think block equivalents
                if think_block_count >= 2:
                    filtered_data.append(row)
                else:
                    removed_count += 1
                    # Optionally print examples of removed rows (first few)
                    if removed_count <= 3:
                        print(f"  Example removed row {removed_count}: {think_block_count} think block equivalent(s)")
            else:
                # Keep rows that don't have 'output' field or aren't dictionaries
                filtered_data.append(row)

        print(f"Rows removed (with 0 or 1 think block equivalent): {removed_count}")
        print(f"Rows remaining: {len(filtered_data)}")

        # Save the filtered data
        print(f"Saving filtered data to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, indent=2, ensure_ascii=False)

        print("Filtering completed successfully!")

    except FileNotFoundError:
        print(f"Error: File {input_file} not found.")
    except Exception as e:
        print(f"An error occurred: {str(e)}")

def analyze_think_blocks(input_file: str) -> None:
    """
    Analyze the distribution of think block equivalents in the dataset.

    Args:
        input_file (str): Path to input JSON file
    """
    try:
        print(f"Analyzing think block distribution in {input_file}...")

        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        think_counts = {}

        for row in data:
            if 'output' in row:
                count = count_think_blocks(row['output'])
                think_counts[count] = think_counts.get(count, 0) + 1

        print("\nThink block equivalent distribution:")
        for count in sorted(think_counts.keys()):
            print(f"  {count} think block equivalent(s): {think_counts[count]} rows")

        print(f"\nRows with 0 think block equivalents: {think_counts.get(0, 0)}")
        print(f"Rows with exactly 1 think block equivalent: {think_counts.get(1, 0)}")
        print(f"Rows with 2+ think block equivalents: {sum(think_counts.get(i, 0) for i in think_counts.keys() if i >= 2)}")

    except Exception as e:
        print(f"An error occurred during analysis: {str(e)}")

def filter_large_jsonl(input_file: str, output_file: str) -> None:
    """
    Memory-efficient filtering for very large JSONL files.
    Processes line by line without loading entire file into memory.
    Removes rows with 0 or 1 think block equivalents, keeps rows with 2+.

    Args:
        input_file (str): Path to input JSONL file
        output_file (str): Path to output JSONL file
    """
    try:
        print(f"Processing large JSONL file: {input_file}")

        removed_count = 0
        kept_count = 0

        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:

            for line_num, line in enumerate(infile):
                if line_num % 1000 == 0:
                    print(f"Processing line {line_num}...")

                line = line.strip()
                if not line:
                    continue

                try:
                    row = json.loads(line)

                    if isinstance(row, dict) and 'output' in row:
                        think_block_count = count_think_blocks(row['output'])

                        if think_block_count >= 2:
                            # Keep this row
                            outfile.write(json.dumps(row, ensure_ascii=False) + '\n')
                            kept_count += 1
                        else:
                            removed_count += 1
                    else:
                        # Keep rows without 'output' field
                        outfile.write(json.dumps(row, ensure_ascii=False) + '\n')
                        kept_count += 1

                except json.JSONDecodeError as e:
                    print(f"Warning: Skipping invalid JSON on line {line_num + 1}: {e}")

        print(f"Processing completed!")
        print(f"Rows removed (with 0 or 1 think block equivalent): {removed_count}")
        print(f"Rows kept: {kept_count}")

    except Exception as e:
        print(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    input_filename = "dataset1.json"
    output_filename = "2_clean.json"

    print("=" * 50)
    print("JSON Filter Tool - Remove rows with 0 or 1 think block equivalent")
    print("(<tool_call> counts as 2 think blocks)")
    print("=" * 50)

    # Check file size to decide processing method
    import os
    try:
        file_size = os.path.getsize(input_filename)
        file_size_mb = file_size / (1024 * 1024)
        print(f"File size: {file_size_mb:.2f} MB")

        # Always try to process as regular JSON first
        print("Processing as regular JSON array...")

        # First, analyze the data
        analyze_think_blocks(input_filename)

        print("\n" + "=" * 50)

        # Then filter the data
        filter_json_data(input_filename, output_filename)

    except FileNotFoundError:
        print(f"File {input_filename} not found. Please make sure the file exists.")

    print("\n" + "=" * 50)
    print("Process completed!")
    print(f"Original file: {input_filename}")
    print(f"Filtered file: {output_filename}")
